import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Caregivers } from '../../entities/caregivers.entity';
import { Organization } from '../../entities/organization.entity';
import { CaregiversService } from '../../services/caregivers.service';
import { CaregiversController } from './caregivers.controller';
import { OrganizationService } from 'src/services/organization.service';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { User } from 'src/entities/user.entity';
import { MongooseModule } from '@nestjs/mongoose';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositorySchema,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import {
  ClientsRepository,
  ClientsRepositorySchema,
} from 'src/entities/mongodb/clientsrepository.entity';
import {
  ESignRequestLogsRepository,
  ESignRequestLogsSchema,
} from 'src/entities/mongodb/e-sign-request-log.entity';
import {
  FormSectionsRepository,
  FormSectionsRepositorySchema,
} from 'src/entities/mongodb/form-sections.entity';
import {
  FormFields,
  FormFieldsSchema,
} from 'src/entities/mongodb/formfields.entity';
import {
  FormsRepository,
  FormsRepositorySchema,
} from 'src/entities/mongodb/formsrepository.entity';
import {
  FormValuesRepository,
  FormValuesRepositorySchema,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import {
  OrgFormsRepository,
  OrgFormsRepositorySchema,
} from 'src/entities/mongodb/orgformsrepository.entity';
import {
  PrimaryFormsRepository,
  PrimaryFormsRepositorySchema,
} from 'src/entities/mongodb/primary-forms-repository.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import EncryptionService from 'src/services/encryption.service';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';
import { FormFieldsService } from 'src/services/formfields.service';
import { FormsRepositoryService } from 'src/services/formsrespository.service';
import { FormValueService } from 'src/services/formvalues.service';
import { GlobalService } from 'src/services/global.service';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { SendGridService } from 'src/util/sendgrid.service';
import { AppsModule } from '../apps/apps.module';
import { ConfigurationsModule } from '../configurations/configurations.module';
import { UserModule } from '../user/user.module';
import { ClientsExistsRule } from 'src/util/validations/caregiver.validations';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { InjectRequestInterceptor } from 'src/util/interceptor/request.interceptor';

@Module({
  imports: [
    UserModule,
    AppsModule,
    TypeOrmModule.forFeature(
      [
        OnboardingEmployee,
        User,
        Organization,
        IndustryTypes,
        OrgAppConfiguration,
        Configurations,
        IndustryAppProcess,
        Caregivers,
      ],
      'mysql',
    ),
    MongooseModule.forFeature([
      { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
      { name: FormsRepository.name, schema: FormsRepositorySchema },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      { name: ClientsRepository.name, schema: ClientsRepositorySchema },
      { name: FormFields.name, schema: FormFieldsSchema },
      { name: ESignRequestLogsRepository.name, schema: ESignRequestLogsSchema },
      {
        name: AppOrgFormsToggleRepository.name,
        schema: AppOrgFormsToggleRepositorySchema,
      },
      {
        name: PrimaryFormsRepository.name,
        schema: PrimaryFormsRepositorySchema,
      },

      {
        name: FormSectionsRepository.name,
        schema: FormSectionsRepositorySchema,
      },
      {
        name: OnBoardEmpChecklistRepository.name,
        schema: OnBoardEmpChecklistRepositorySchema,
      },
      {
        name: OnBoardOrgChecklistRepository.name,
        schema: OnBoardOrgChecklistRepositorySchema,
      },
    ]),
    ConfigurationsModule,
  ],
  providers: [
    OnboardingEmployeeService,
    OrganizationService,
    OrgFormsRepositoryService,
    IndustryTypesService,
    FormsRepositoryService,
    GlobalService,
    FormFieldsService,
    FormSectionsRepositoryService,
    FormValueService,
    ClientsRepositoryService,
    SendGridService,
    EncryptionService,
    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
    CaregiversService,
    ClientsExistsRule,
    {
      provide: APP_INTERCEPTOR,
      useClass: InjectRequestInterceptor,
    },
  ],
  controllers: [CaregiversController],
  exports: [CaregiversService],
})
export class CaregiversModule {}

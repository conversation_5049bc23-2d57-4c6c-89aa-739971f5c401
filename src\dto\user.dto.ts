import { Request } from '@nestjs/common';
import {
  IsNotEmpty,
  IsEmail,
  IsMobilePhone,
  IsOptional,
  IsArray,
  ArrayNotEmpty,
  ArrayUnique,
  IsString,
} from 'class-validator';
import { Organization } from 'src/entities/organization.entity';
import { OrganizationExists } from 'src/util/validations/organization.validation';
import {
  UserMailExists,
  UserMobileExists,
} from 'src/util/validations/user.validation';

export class UserRegisterDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @OrganizationExists()
  organization: Organization;

  @IsNotEmpty()
  password: string;

  @IsEmail()
  @UserMailExists()
  email: string;

  @IsNotEmpty()
  @IsString()
  @UserMobileExists({ context: Request })
  mobile_number: string;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  apps: string[];
}

export class UserDto {
  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  @OrganizationExists()
  organization: string;

  @IsOptional()
  password: string;

  @IsEmail()
  email: string;

  @IsString()
  @IsNotEmpty()
  mobile_number: string;

  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  apps: string[];
}

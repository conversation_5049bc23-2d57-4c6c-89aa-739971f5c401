import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import {
  SchemaOptions,
  ObjectId,
  SchemaTypes,
  HydratedDocument,
} from 'mongoose';
import { OrgFormsRepository } from './orgformsrepository.entity';
import { ClientsRepository } from './clientsrepository.entity';
import { ESignRequestLogsRepository } from './e-sign-request-log.entity';
import { PrimaryFormsRepository } from './primary-forms-repository.entity';

const formValuesRepoSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'form_values_repository',
  versionKey: false,
};
@Schema(formValuesRepoSchemaOptions)
export class FormValuesRepository {
  @Prop({ type: SchemaTypes.ObjectId })
  id: ObjectId;

  @Prop({ unique: true, required: true })
  form_value_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  user_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  organization_id: string;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'OrgFormsRepository',
    required: true,
  })
  form: OrgFormsRepository;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'PrimaryFormsRepository',
    required: false,
  })
  primary_form: PrimaryFormsRepository;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'Client',
    required: false,
  })
  client: ClientsRepository;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'ESignRequestLogsRepository',
    required: false,
  })
  e_sign_request_logs: ESignRequestLogsRepository;

  @Prop({ type: SchemaTypes.Boolean, required: true, default: false })
  values_encryption_status: boolean;

  @Prop({ type: SchemaTypes.Mixed, required: true })
  values: any;

  @Prop({ default: false, type: SchemaTypes.Boolean })
  completed: boolean;

  @Prop({ required: false, type: SchemaTypes.Number })
  total_points: number;

  @Prop({ required: false, type: SchemaTypes.Number })
  scored_points: number;

  @Prop({ type: SchemaTypes.Date })
  deleted_at: Date;
}

export type FormValuesRepositoryDocument =
  HydratedDocument<FormValuesRepository>;
export const FormValuesRepositorySchema =
  SchemaFactory.createForClass(FormValuesRepository);

import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as SendGrid from '@sendgrid/mail';
import { Configurations } from 'src/entities/configurations.entity';
import { OrganizationService } from 'src/services/organization.service';
import { Repository } from 'typeorm';

@Injectable()
export class SendGridService {
  constructor(
    @InjectRepository(Configurations, 'mysql')
    private configurationsRepository: Repository<Configurations>,
    
    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,
  ){}
  
  async send(mail: SendGrid.MailDataRequired, apiKey: string) {
    try{
      if(!apiKey){
        throw new HttpException('API Key is required', HttpStatus.BAD_REQUEST);
      }

      SendGrid.setApiKey(apiKey);
      
      const transport = await SendGrid.send(mail);
      
      return transport;
      
    }catch(error){
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    
  }

  async sendWithOrganizationId(organization_id:string, mail: SendGrid.MailDataRequired){
    try {
      const organization = await this.organizationService.findOne(
        { organization_id },
        false,
        { id: true },
      );

      const emailConfigDetails: any =
        await this.configurationsRepository.findOne({
          where: {
            organization,
            type: 'email',
          },
        });

      if (emailConfigDetails && emailConfigDetails.name == 'send-grid') {
        mail.from = emailConfigDetails?.details?.fromEmail;
        return await this.send(mail, emailConfigDetails.details.apiKey);
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

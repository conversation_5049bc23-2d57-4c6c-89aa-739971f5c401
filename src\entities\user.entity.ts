import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';

import { Apps } from './apps.entity';
import { Organization } from './organization.entity';
import { OnboardingEmployee } from './onboarding-employee.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  user_id: string;

  @Column()
  name: string;

  @Column()
  email: string;

  @Column()
  mobile_number: string;

  @Column()
  password: string;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @OneToOne(
    () => OnboardingEmployee,
    (onboarding_employee: OnboardingEmployee) => onboarding_employee.id,
    { eager: false, nullable: true },
  )
  @JoinColumn()
  onboarding_employee: OnboardingEmployee;

  @ManyToMany(() => Apps, (app: Apps) => app.users, {
    eager: false,
    nullable: false,
  })
  @JoinTable({
    name: 'user_apps',
    joinColumn: {
      name: 'user_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'app_id',
      referencedColumnName: 'id',
    },
  })
  apps: Apps[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

@Entity('user_apps')
export class UserApps {
  @PrimaryColumn({ type: 'int' })
  app_id: number;

  @PrimaryColumn({ type: 'int' })
  user_id: number;

  @OneToOne(() => User)
  @JoinTable()
  user: User;

  @OneToOne(() => Apps)
  @JoinTable()
  app: Apps;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

import {
  Controller,
  Get,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
  HttpException,
  HttpStatus,
  Put,
  UseGuards,
  Req,
  Param,
  Res,
  Patch,
  Delete,
  Query,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request, Response } from 'express';
import { CreateClientRepositoryDto } from 'src/dto/clientsrepository.dto';
import { UserAuth } from 'src/guards/user.guard';
import { AppsService } from 'src/services/apps.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';

@Controller('clients-repository')
export default class ClientsRepositoryController {
  constructor(
    private clientsRepositoryService: ClientsRepositoryService,
    private appService: AppsService,
  ) {}

  @Post('')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async crate(
    @Body() body: CreateClientRepositoryDto,
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<Response> {
    try {
      /*    body.user_id = request.user['user_id'];
      body.app_id = request.user['app_id']; */

      const data = await this.clientsRepositoryService.create(
        body,
        request.user,
        request.headers['kleza-app-code'],
      );

      return data
        ? response.send({
            status: true,
            message: 'Client created successfully',
            data: data,
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable create the client',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the client by client_id
   * @param body CreateClientRepositoryDto
   * @param client_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('/:client_id')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  @UsePipes(ValidationPipe)
  async update(
    @Body() body: CreateClientRepositoryDto,
    @Param('client_id') client_id: string,
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<Response> {
    try {
      const data = await this.clientsRepositoryService.update(client_id, body);

      return data
        ? response.send({
            status: true,
            message: 'Client updated Successfully',
            data: data,
          })
        : response.status(500).send({
            status: false,
            message: 'Unable to update the field',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * get the client details by client id
   * @param client_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/:client_id')
  @UseGuards(AuthGuard('jwt'))
  async findOne(
    @Param('client_id') client_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.clientsRepositoryService.findOne({ client_id });

      return data
        ? response.send({
            status: true,
            message: 'Client details fetched Successfully',
            data: data,
          })
        : response.status(500).send({
            status: false,
            message: 'Unable to update the field',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * get all clients
   * @param response Response
   * @returns Promise<Response[]>
   */
  @Get('')
  @UseGuards(AuthGuard('jwt'))
  async findAll(
    @Res() response: Response,
    @Req() request: Request,
    @Query('app_id') app_id?: string,
  ): Promise<Response> {
    try {
      let organization_id: string;

      if (request.user['isOrganization']) {
        organization_id = request?.user['organization_id'];
      } else {
        organization_id = request.user['organization']['organization_id'];

        const appCode: any = request?.headers['kleza-app-code'];

        if (request.user && appCode) {
          const app = await this.appService.findOne(
            { app_code: appCode },
            false,
            { id: true, app_id: true },
          );

          const user: any = request.user;

          const checkingAppCode = user.apps.filter(
            (app) => app.app_code === appCode,
          );

          if (checkingAppCode.length) {
            app_id = app.app_id;
          } else {
            throw new UnauthorizedException();
          }
        } else {
          throw new UnauthorizedException();
        }
      }

      return response.send({
        status: true,
        message: 'Clients details fetched Successfully',
        data: await this.clientsRepositoryService.findAll({
          app_id,
          organization_id,
        }),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete the Client
   * @param client_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Delete('/:client_id')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(ValidationPipe)
  async deleteField(
    @Param('client_id') client_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.clientsRepositoryService.delete(client_id);
      return data
        ? response.send({
            status: true,
            message: 'Client is delete successfully',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to delete the client',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

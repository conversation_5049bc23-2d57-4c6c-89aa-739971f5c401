import { Injectable } from '@nestjs/common';
import {
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { OrganizationService } from 'src/services/organization.service';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { ILike } from 'typeorm';

@ValidatorConstraint({ name: 'OrganizationExists', async: true })
@Injectable()
export class OrganizationExistsRule implements ValidatorConstraintInterface {
  constructor(private organizationService: OrganizationService) {}

  async validate(value: any) {
    try {
      return (await this.organizationService.findOne(
        {
          organization_id: value,
        },
        false,
      ))
        ? true
        : false;
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Organization doesn't exist`;
  }
}

export function OrganizationExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'OrganizationExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: OrganizationExistsRule,
    });
  };
}
@ValidatorConstraint({ name: 'OrganizationMailExists', async: true })
@Injectable()
export class OrganizationMailExistsRule
  implements ValidatorConstraintInterface
{
  constructor(private organizationService: OrganizationService) {}

  async validate(value: number) {
    try {
      return (await this.organizationService.findOne({ email: value }, true))
        ? false
        : true;
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Entered email id was already connected with another organization.`;
  }
}

export function OrganizationMailExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'OrganizationMailExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: OrganizationMailExistsRule,
    });
  };
}
@ValidatorConstraint({ name: 'OrganizationMobileExists', async: true })
@Injectable()
export class OrganizationMobileExistsRule
  implements ValidatorConstraintInterface
{
  constructor(private organizationService: OrganizationService) {}

  async validate(value: number) {
    try {
      return (await this.organizationService.findOne(
        { mobile_number: value },
        true,
      ))
        ? false
        : true;
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Entered mobile number was already connected with another organization.`;
  }
}

export function OrganizationMobileExists(
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'OrganizationMobileExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: OrganizationMobileExistsRule,
    });
  };
}

@ValidatorConstraint({ name: 'OrganizationNameExists', async: true })
@Injectable()
export class OrganizationNameExistsRule
  implements ValidatorConstraintInterface
{
  constructor(private organizationService: OrganizationService) {}

  async validate(value: string) {
    try {
      return (await this.organizationService.findOne(
        { name: value.trim() },
        true,
      ))
        ? false
        : true;
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Entered organization name was already connected with another organization.`;
  }
}

export function OrganizationNameExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'OrganizationNameExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: OrganizationNameExistsRule,
    });
  };
}

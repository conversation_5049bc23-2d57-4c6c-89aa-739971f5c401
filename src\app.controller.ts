import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';

import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Post('zoho-webhook')
  zohoWebhook(@Body() body: any) {
    try {
      console.log('body', body);
      return {
        status: true,
        message: 'Successfully updated',
      };
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/demoFlowJson')
  async readDemoFlowJson(
    @Query('onboarding_employee_id') onboarding_employee_id?: string,
  ) {
    try {
      const response = await this.appService.readJsonData(
        onboarding_employee_id,
      );
      return response;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('/demoFlowJson')
  async writeDemoFlowJson(@Body() body: any) {
    try {
      await this.appService.storeJsonData(body);
      return {
        status: true,
        message: 'Succesfully updated',
      };
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import { Injectable } from '@nestjs/common';
import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  ValidationOptions,
  registerDecorator,
} from 'class-validator';
import { CaregiversService } from 'src/services/caregivers.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';

@ValidatorConstraint({ name: 'ClientsExists', async: true })
@Injectable()
export class ClientsExistsRule implements ValidatorConstraintInterface {
  constructor(private clientsRepositoryService: ClientsRepositoryService) {}

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    try {
      if (value) {
        const response = await this.clientsRepositoryService.findOne({
          client_id: value,
        });
        return response ? true : false;
      } else {
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  defaultMessage(): string {
    return `Please select the existed client only`;
  }
}

export function ClientsExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'ClientsExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: ClientsExistsRule,
    });
  };
}

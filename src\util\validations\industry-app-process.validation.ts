import { Injectable } from '@nestjs/common';
import {
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';

@ValidatorConstraint({ name: 'IndustryAppProcessExists', async: true })
@Injectable()
export class IndustryAppProcessExistsRule
  implements ValidatorConstraintInterface
{
  constructor(private industryAppProcessService: IndustryAppProcessService) {}

  async validate(value: any) {
    try {
      return (await this.industryAppProcessService.findOne(
        {
          industry_app_process_id: value,
        },
        false,
      ))
        ? true
        : false;
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Industry Type doesn't exist`;
  }
}

export function IndustryAppProcessExists(
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IndustryAppProcessExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: IndustryAppProcessExistsRule,
    });
  };
}

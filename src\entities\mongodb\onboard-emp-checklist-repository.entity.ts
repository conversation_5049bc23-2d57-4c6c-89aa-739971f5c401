import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  HydratedDocument,
  ObjectId,
  SchemaOptions,
  SchemaTypes,
} from 'mongoose';
import {
  CheckListItem,
  OnBoardOrgChecklistRepository,
} from './onboard-org-checklist-repository.entity';
import { OnboardingEmployee } from '../onboarding-employee.entity';

const OnBoardEmpChecklistRepoSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'onboard_emp_checklist_repository',
  versionKey: false,
};

@Schema(OnBoardEmpChecklistRepoSchemaOptions)
export class OnBoardEmpChecklistRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ unique: true, type: SchemaTypes.String, required: true })
  emp_checklist_id: string;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'OnBoardOrgChecklistRepository',
  })
  onboard_org_checklist: OnBoardOrgChecklistRepository;

  @Prop({
    type: SchemaTypes.String,
    required: true,
  })
  onboarding_employee_id: string;

  @Prop({ type: SchemaTypes.Mixed })
  completed_checklist: [CheckListItem];

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type OnBoardEmpChecklistRepositoryDocument =
  HydratedDocument<OnBoardEmpChecklistRepository>;

export const OnBoardEmpChecklistRepositorySchema = SchemaFactory.createForClass(
  OnBoardEmpChecklistRepository,
);

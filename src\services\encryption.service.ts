import { Injectable } from '@nestjs/common';
import {
  createCipheriv,
  createDecipheriv,
  randomBytes,
  scryptSync,
} from 'crypto';

@Injectable()
export default class EncryptionService {
  //TODO
  /**
   * Encrypt the data using AES algorithm
   * Decrypt encryption string to data
   */
  private keyName: Buffer;
  private secret: string;
  private algorithm: string;

  constructor() {
    this.secret = process.env.ENCRYPT_SECRET;
    this.keyName = scryptSync(this.secret, 'salt', 32);
    this.algorithm = 'aes-256-cbc';
  }

  /**
   * Encrypt the data using aes algorithm
   * @param data any
   * @returns string
   */
  encryption(data: any): string {
    try {
      let isObject = false;

      if (typeof data === 'object') {
        data = JSON.stringify(data);
        isObject = true;
      }

      const marker = isObject ? 'OBJ|' : 'STR|';

      const iv = randomBytes(16);
      const cipher = createCipheriv(this.algorithm, this.keyName, iv);
      const encrypted = cipher.update(marker + data, 'utf8', 'hex');
      return [
        encrypted + cipher.final('hex'),
        Buffer.from(iv).toString('hex'),
      ].join('|');
    } catch (error) {
      throw error;
    }
  }

  encryptObject(data: any, encryptData: string[] = []) {
    Object.keys(data).forEach((key) => {
      if (!encryptData.length) {
        if (data[key]) {
          data[key] = this.encryption(data[key]);
        } else {
          data[key] = '';
        }
      }

      if (encryptData.find((element) => element === key)) {
        if (data[key]) {
          data[key] = this.encryption(data[key]);
        } else {
          data[key] = '';
        }
      }
    });
    return data;
  }

  /**
   * Decrypt the encrypted string to original data
   * @param encryptedData string
   * @return string
   */
  decryption(encryptedData: string): string {
    try {
      const [encrypted, iv] = encryptedData.split('|');
      if (!iv) throw new Error('IV not found');
      const decipher = createDecipheriv(
        this.algorithm,
        this.keyName,
        Buffer.from(iv, 'hex'),
      );

      const decrypted =
        decipher.update(encrypted, 'hex', 'utf8') + decipher.final('utf8');

      const [marker, data] = [decrypted.slice(0, 4), decrypted.slice(4)];

      if (marker === 'OBJ|') {
        return JSON.parse(data);
      }

      return data;
    } catch (error) {
      throw error;
    }
  }

  decryptObject(data: any, decryptData: string[] = []) {
    Object.keys(data).forEach((key) => {
      if (!decryptData.length) {
        if (data[key]) {
          data[key] = this.decryption(data[key]);
        } else {
          data[key] = '';
        }
      }

      if (decryptData.find((element) => element === key)) {
        if (data[key]) {
          data[key] = this.decryption(data[key]);
        } else {
          data[key] = '';
        }
      }
    });

    return data;
  }
}

import {
  Get,
  Req,
  Res,
  Put,
  Body,
  Post,
  Query,
  Param,
  UsePipes,
  UseG<PERSON>s,
  Controller,
  HttpStatus,
  HttpException,
  ValidationPipe,
  Patch,
} from '@nestjs/common';
import { FindOptionsWhere } from 'typeorm';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';

import { Themes } from 'src/entities/themes.entity';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { ThemesService } from 'src/services/themes.service';
import { ThemesDto } from 'src/dto/themes.dto';
import { UserAuth } from 'src/guards/user.guard';
import { appThemes } from 'src/util/constants';
import { AppsService } from 'src/services/apps.service';
import { Apps } from 'src/entities/apps.entity';

@Controller('themes')
export default class ThemesController {
  constructor(
    private ThemesService: ThemesService,
    private readonly appsService: AppsService,
  ) {}

  @Patch('activate/:theme_id')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async activateTheme(
    @Param('theme_id') theme_id: string,
    @Query('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      if (!app_id || !theme_id) {
        response.status(HttpStatus.CONFLICT).json({
          status: false,
          message: 'App Id or Theme Id is missing',
        });
      }

      const organization_id = request.user['organization_id'];

      const data = await this.ThemesService.activateTheme(
        app_id,
        organization_id,
        theme_id,
      );

      return data
        ? response.status(HttpStatus.CREATED).json({
            status: true,
            message: 'Theme activated successfully',
          })
        : response.status(HttpStatus.CONFLICT).json({
            status: false,
            message: 'Unable to update theme. Please try again later',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/active-theme')
  // @UseGuards(AuthGuard('jwt'), UserAuth)
  async getActiveTheme(
    @Req() request: any,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const organization_id = request.headers['organization-id'];

      const apps = await this.appsService.findOne(
        { app_code: request.headers['kleza-app-code'] },
        false,
        { id: true, app_id: true },
        { industry_app_process: true },
      );

      if (!organization_id) {
        return response.json({
          theme: appThemes(apps?.industry_app_process?.process_code),
          status: true,
          message: 'Theme Get Successfully.',
        });
      }

      const findBy: FindOptionsWhere<Themes> = {
        status: true,
      };

      findBy.organization = await this.ThemesService.getOrganization(
        organization_id,
      );

      findBy.apps = [await this.ThemesService.getApp(apps.app_id)];

      const data = await this.ThemesService.findOne(findBy, false, {
        ...this.ThemesService.select,
        id: true,
      });

      const theme = data
        ? data?.theme
        : appThemes(apps?.industry_app_process?.process_code);

      return response.json({
        theme,
        status: true,
        message: 'Theme Get Successfully.',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/me')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async getMyThemes(
    @Query('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const findBy: FindOptionsWhere<Themes> = {};

      const organization_id = request.user['organization_id'];

      findBy.organization = await this.ThemesService.getOrganization(
        organization_id,
      );

      if (app_id) {
        findBy.apps = await this.ThemesService.getApp(app_id);
      }

      const data = await this.ThemesService.list(findBy);

      return data
        ? response.json({
            data,
            status: true,
            message: 'Theme get successfully',
          })
        : response.status(HttpStatus.NOT_FOUND).json({
            status: false,
            message: 'App theme not found',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:theme_id')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async getThemeById(
    @Param('theme_id') theme_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const findBy: FindOptionsWhere<Themes> = {
        theme_id,
      };
      const organization_id = request.user['organization_id'];
      findBy.organization = await this.ThemesService.getOrganization(
        organization_id,
      );
      const data = await this.ThemesService.findOne(findBy, false, {
        ...this.ThemesService.select,
        id: true,
      });
      if (data) {
        delete data.id;
        return response.json({
          data,
          status: true,
          message: 'Theme get successfully',
        });
      } else {
        return response.status(HttpStatus.NOT_FOUND).json({
          status: false,
          message: 'App thems not found',
        });
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  //Save Theme
  @Post('/:app_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async addTheme(
    @Body() body: ThemesDto,
    @Param('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const organization_id = request.user['organization_id'];
      const data = await this.ThemesService.insert(
        body,
        app_id,
        organization_id,
      );
      return data
        ? response.status(HttpStatus.CREATED).json({
            status: true,
            message: 'Theme Added Successfully',
          })
        : response.status(HttpStatus.CONFLICT).json({
            status: false,
            message: 'Unable to add theme. Please try again later',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/:theme_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async updateTheme(
    @Body() body: ThemesDto,
    @Param('theme_id') theme_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const organization_id = request.user['organization_id'];
      const data = await this.ThemesService.update(
        theme_id,
        body,
        organization_id,
      );
      return data
        ? response.status(HttpStatus.CREATED).json({
            status: true,
            message: 'Theme Updated Successfully',
          })
        : response.status(HttpStatus.CONFLICT).json({
            status: false,
            message: 'Unable to update theme. Please try again later',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

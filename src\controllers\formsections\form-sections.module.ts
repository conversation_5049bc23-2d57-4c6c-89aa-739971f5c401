import { Modu<PERSON> } from '@nestjs/common';
import {
  FormSectionsRepository,
  FormSectionsRepositorySchema,
} from 'src/entities/mongodb/form-sections.entity';
import { MongooseModule } from '@nestjs/mongoose';

import { FormSectionRepositoryController } from './form-sections.controller';
import { FormSectionExistRule } from 'src/util/validations/forms.validations';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: FormSectionsRepository.name,
        schema: FormSectionsRepositorySchema,
      },
    ]),
  ],
  providers: [FormSectionsRepositoryService, FormSectionExistRule],
  controllers: [FormSectionRepositoryController],
})
export class FormSectionsRepositoryModule {}

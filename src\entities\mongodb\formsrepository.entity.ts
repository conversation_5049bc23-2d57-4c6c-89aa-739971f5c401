import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  HydratedDocument,
  ObjectId,
  SchemaOptions,
  SchemaTypes,
} from 'mongoose';
import { GROUP } from 'src/util/interfaces/forms.interface';

const FormsRepoSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'forms_repository',
  versionKey: false,
};
@Schema(FormsRepoSchemaOptions)
export class FormsRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ unique: true, type: SchemaTypes.String, required: true })
  form_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  name: string;

  @Prop({ type: SchemaTypes.String, default: null })
  icon: string;

  @Prop({ type: SchemaTypes.Number, default: 1 })
  order_position: number;

  @Prop({ type: SchemaTypes.String, default: null })
  description: string;

  @Prop({ type: SchemaTypes.String })
  app_id: string;

  @Prop({ type: SchemaTypes.Mixed })
  fields: Record<string, GROUP>;

  @Prop({ type: SchemaTypes.Mixed })
  validationschema: any;

  @Prop({ type: SchemaTypes.Boolean, default: false })
  has_sub_forms: boolean;

  @Prop({ type: SchemaTypes.Boolean, default: false })
  is_sub_form: boolean;

  @Prop({ type: SchemaTypes.Boolean, default: false })
  is_quiz_form: boolean;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'FormsRepository' })
  main_form_id: ObjectId;

  @Prop({
    type: [{ type: SchemaTypes.ObjectId, ref: 'FormsRepository' }],
    default: [],
  })
  sub_forms: FormsRepository[];

  @Prop({ default: false, type: SchemaTypes.Boolean })
  status: boolean;

  @Prop({ default: true, type: SchemaTypes.Boolean })
  is_disable_form_response: boolean;

  @Prop({ default: false, type: SchemaTypes.Boolean })
  has_multiple_form_responses: boolean;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type FormsRepositoryDocument = HydratedDocument<FormsRepository>;
export const FormsRepositorySchema =
  SchemaFactory.createForClass(FormsRepository);

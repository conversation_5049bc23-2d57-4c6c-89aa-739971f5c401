import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  In,
  Not,
  Repository,
} from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { HttpStatus, HttpException, Injectable } from '@nestjs/common';

import {
  AddFormOrganization,
  AddFormsOrganization,
  OrganizationRegisterDto,
  OrganizationUpdateDto,
  RemoveFormOrganization,
  UpdateFormsStatusDto,
} from 'src/dto/organization.dto';
import { AppsService } from './apps.service';
import { generateBcryptHash, generateUUID } from 'src/util';
import { Organization } from 'src/entities/organization.entity';
import { OrgFormsRepositoryService } from './orgformsrepository.service';
import { IndustryTypesService } from './industry-types.service';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { SendGridService } from 'src/util/sendgrid.service';
import { Configurations } from 'src/entities/configurations.entity';
import { MailDataRequired } from '@sendgrid/mail';
import { User } from 'src/entities/user.entity';
import EncryptionService from './encryption.service';

@Injectable()
export class OrganizationService {
  constructor(
    @InjectRepository(Organization, 'mysql')
    private OrganizationRepository: Repository<Organization>,

    @InjectRepository(OrgAppConfiguration, 'mysql')
    private orgAppConfigurationRepository: Repository<OrgAppConfiguration>,

    @InjectRepository(Configurations, 'mysql')
    private ConfigurationsRepository: Repository<Configurations>,

    @InjectRepository(User, 'mysql')
    private UserRepository: Repository<User>,

    private OrgFormsRepositoryService: OrgFormsRepositoryService,

    private appsService: AppsService,

    private industryTypesService: IndustryTypesService,

    private sendGridService: SendGridService,

    private encryptionService: EncryptionService,
  ) {}

  /**
   * @variable select
   */
  public select: FindOptionsSelect<Organization> = {
    name: true,
    email: true,
    status: true,
    logo: true,
    created_at: true,
    updated_at: true,
    mobile_number: true,
    organization_id: true,
  };
  /**
   * OnBoard Organizations With Apps
   * @param data OrganizationRegisterDto
   * @returns User
   */
  async createOrganization(
    data: OrganizationRegisterDto,
  ): Promise<Organization | any> {
    data.password = await generateBcryptHash(data.password, 10);
    try {
      let apps = await this.appsService.list(
        { app_id: In(data.apps) },
        { id: true },
        false,
        { industry_app_process: true },
      );

      if (!apps.length) {
        throw new HttpException('Invalid Apps', HttpStatus.BAD_REQUEST);
      }

      const industryAppProcess = [];

      apps = apps.map((app) => {
        if (
          industryAppProcess.includes(app?.industry_app_process?.process_code)
        ) {
          throw new HttpException(
            'Duplicate process apps not allowed for same organization',
            HttpStatus.BAD_REQUEST,
          );
        } else {
          industryAppProcess.push(app?.industry_app_process?.process_code);
        }
        delete app.industry_app_process;
        return app;
      });

      const industry_type = await this.industryTypesService.findOne(
        { industry_type_id: data.industry_type_id },
        false,
        { id: true },
      );

      const organization_id = generateUUID();

      const pass_key = this.encryptionService.encryption(organization_id);

      const organization = await this.OrganizationRepository.save({
        ...data,
        apps,
        industry_type,
        organization_id,
        pass_key,
      });

      try {
        const mail = {
          to: organization.email,
          subject: 'Welcome',
          from: process.env.FROM_EMAIL,
          text: 'Hai',
          html: `
                <html>
                  <head></head>
                  <body>
                      <h5>Dear ${organization.name}, </h5>
                      <br/>
                      <p>Thank you for choosing Kleza FAB! </p>
                      <br/>
                      <p>We are excited to welcome you as an Organization Admin. Your registration has been successfully completed, and you are now equipped to manage and oversee your organization's settings and users. </p>
                      <br/>
                      <p>If you have any questions or require assistance, our support team is here to help. Feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a> </p>
                      <br/>
                      <p>Thank you once again for joining Kleza FAB. We look forward to supporting you in achieving your organizational goals. </p>
                      <br/>
                      <p>The Kleza FAB Team </p>
                      </body>
                </html>
          `,
        };

        await this.sendGridService.send(mail, process.env.API_KEY);
      } catch (error) {}

      return organization;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get List of organizations
   * @returns Promise<Organization[]>
   */
  async list(
    where: FindOptionsWhere<Organization> = null,
    select: FindOptionsSelect<Organization> = this.select,
    withDeleted: boolean = false,
  ): Promise<Organization[]> {
    try {
      return await this.OrganizationRepository.find({
        where: where,
        withDeleted,
        select,
        relations: {
          apps: {
            industry_app_process: true,
          },
          user: { apps: true },
        },
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get Organization details by condition
   * @param findBy Object "{column: value}"
   * @returns Organization
   */
  async findOne(
    findBy: any,
    withDeleted = false,
    select: FindOptionsSelect<Organization> = this.select,
    relations: FindOptionsRelations<Organization> = {},
  ): Promise<Organization> {
    try {
      let data = await this.OrganizationRepository.findOne({
        where: findBy,
        withDeleted,
        select: select,
        relations,
      });

      const organization = await this.OrganizationRepository.findOne({
        where: findBy,
        withDeleted: false,
        select: { id: true },
      });

      const relationKeys = Object.keys(relations);

      if (relationKeys.length > 0) {
        if (relationKeys.includes('apps')) {
          data.apps = await Promise.all(
            data.apps.map(async (app: any) => {
              app.orgAppConfiguration =
                await this.orgAppConfigurationRepository.findOne({
                  where: {
                    app: { id: app.id },
                    organization: organization,
                  },
                });
              delete app?.id;
              return app;
            }),
          );
        }
      }

      return data;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Check organization was exist or not using email
   * @param email string
   * @returns boolean | Organization
   */
  async checkOrganizationExistOrNot(
    email: string,
    id?: string,
  ): Promise<Organization> {
    const find: any = { email };
    if (id) find.organization_id = Not(id);
    return await this.findOne(find);
  }

  /**
   * Check organization was exist or not using mobile number
   * @param mobile_number string
   * @returns boolean | Organization
   */
  async checkOrganizationMobileExistOrNot(
    mobile_number: string,
    id?: string,
  ): Promise<Organization> {
    const find: any = { mobile_number };
    if (id) find.organization_id = Not(id);
    return await this.findOne(find);
  }

  /**
   * Update organization
   * @param data OrganizationRegisterDto
   * @returns User
   */
  async updateOrganization(
    data: OrganizationUpdateDto,
    id: string,
    response: any,
  ): Promise<any> {
    try {
      const organization = await this.findOne(
        { organization_id: id },
        false,
        {
          ...this.select,
          id: true,
        },
        {
          apps: true,
        },
      );

      if (!organization) {
        throw new HttpException('Organization Not Found', HttpStatus.NOT_FOUND);
      }

      if (await this.checkOrganizationExistOrNot(data.email, id))
        throw new HttpException(
          'Entered email id was already connected with another organization.',
          HttpStatus.CONFLICT,
        );

      if (await this.checkOrganizationMobileExistOrNot(data.mobile_number, id))
        throw new HttpException(
          'Entered mobile number was already connected with another organization.',
          HttpStatus.CONFLICT,
        );

      if (data.password)
        data.password = await generateBcryptHash(data.password, 10);

      if (!organization.pass_key)
        data.pass_key = this.encryptionService.encryption(
          organization.organization_id,
        );

      //TODO: Have to optimize this logic
      if (
        data?.check_org_apps_assigned_to_user &&
        organization?.apps?.length > data.apps?.length
      ) {
        const existingAppIds = organization.apps.map((app) => app.app_id);

        const newAppIds = data.apps;

        const removedAppIds = existingAppIds.filter(
          (app_id) => !newAppIds.includes(app_id),
        );

        if (removedAppIds.length > 0) {
          const usersInOrganization = await this.UserRepository.find({
            where: {
              organization: { id: organization.id },
            },
            relations: { apps: true },
          });

          const usersAssignedToRemovedApps = usersInOrganization.filter(
            (user) =>
              user.apps.some((app) => removedAppIds.includes(app.app_id)),
          );

          if (
            data?.check_org_apps_assigned_to_user &&
            data?.confirm_remove_user_apps
          ) {
            usersAssignedToRemovedApps.forEach(async (user) => {
              user.apps = user.apps.filter(
                (app) => !removedAppIds.includes(app.app_id),
              );
              await this.UserRepository.save(user);
            });
          } else {
            if (usersAssignedToRemovedApps.length > 0) {
              return response.json({
                status: false,
                message:
                  'Some users are still assigned to the apps being removed. Confirm removal before proceeding.',
                users_existed_for_the_removed_apps: true,
              });
            }
          }
        }
      }

      let apps = await this.appsService.list(
        { app_id: In(data.apps) },
        { id: true },
        false,
        { industry_app_process: true },
      );

      const industryAppProcess = [];

      apps = apps.map((app) => {
        if (
          industryAppProcess.includes(app?.industry_app_process?.process_code)
        ) {
          throw new HttpException(
            'Duplicate process apps not allowed for same organization',
            HttpStatus.BAD_REQUEST,
          );
        } else {
          industryAppProcess.push(app?.industry_app_process?.process_code);
        }
        delete app.industry_app_process;
        return app;
      });

      return response.send({
        status: true,
        message: 'Organization updated successfully',
        data: await this.OrganizationRepository.save(
          {
            id: organization.id,
            ...data,
            apps,
          },
          { reload: true },
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Add Multiple Forms To Organization
   * @param organiztion_id string
   * @param body AddFormsOrganization
   * @param user Express.User
   * @returns Promise<Boolean>
   */
  async addFormsToOrganization(
    organiztion_id: string,
    body: AddFormsOrganization,
    user: Express.User,
  ): Promise<Boolean> {
    try {
      const data = await this.OrgFormsRepositoryService.insertFormByFormIds(
        body.form_ids,
        organiztion_id,
        user,
      );
      return data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Remove form from Organization
   * @param organiztion_id string
   * @param body RemoveFormOrganization
   * @param user Express.User
   * @returns Promise<Boolean>
   */
  async removeFormToOrganization(
    organiztion_id: string,
    body: RemoveFormOrganization,
    user: Express.User,
  ): Promise<Boolean> {
    try {
      const data = await this.OrgFormsRepositoryService.removeForm(
        organiztion_id,
        body.form_id,
        user,
      );
      return data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add Form to Organization
   * @param organiztion_id string
   * @param body AddFormOrganization
   * @param user Express.User
   * @returns Promise<Boolean>
   */
  async addFormToOrganization(
    organiztion_id: string,
    body: AddFormOrganization,
    user: Express.User,
  ): Promise<Boolean> {
    try {
      const data = await this.OrgFormsRepositoryService.addForm(
        organiztion_id,
        body.form_id,
        user,
      );
      return data ? true : false;
    } catch (error) {
      throw error;
    }
  }

  async updateFormsStatus({
    form_ids,
    status,
  }: UpdateFormsStatusDto): Promise<boolean> {
    try {
      const data = await this.OrgFormsRepositoryService.bulkUpdate(
        {
          form_id: { $in: form_ids },
        },
        { status },
      ).catch((error) => {
        throw error;
      });
      return data ? true : false;
    } catch (error) {
      throw error;
    }
  }

  async sendMail(organization_id, { email, content, subject }) {
    try {
      const organization = await this.findOne({ organization_id }, false, {
        id: true,
      });

      const configuration = await this.ConfigurationsRepository.findOne({
        where: {
          organization,
          type: 'email',
        },
      });

      if (!configuration || configuration === null) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: 'Please Do E-Mail Configuration and try this',
        };
      }

      const mail: MailDataRequired = {
        to: email,
        subject: subject,
        from: configuration.details.fromEmail,
        text: ' ',
        html: content,
      };

      await this.sendGridService.send(mail, configuration.details.apiKey);

      return {
        status: true,
        message: 'Mail Sent Successfully',
      };
    } catch (error) {
      throw error;
    }
  }

  //Remove Organization
  removeOrganization(organization_id: string): Promise<any> {
    try {
      return this.OrganizationRepository.softDelete({ organization_id });
    } catch (error) {
      throw error;
    }
  }
}

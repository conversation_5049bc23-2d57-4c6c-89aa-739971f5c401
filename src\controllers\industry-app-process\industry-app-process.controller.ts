import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Res,
  Query,
  NotFoundException,
} from '@nestjs/common';
import { Response } from 'express';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';

import { IndustryTypesService } from 'src/services/industry-types.service';

@Controller('industry-app-process')
export default class IndustryAppProcessController {
  constructor(
    private industryTypesService: IndustryTypesService,
    private industryAppProcessService: IndustryAppProcessService,
  ) {}

  @Get('/')
  async find(
    @Res() response: Response,
    @Query('industry_type_id') industry_type_id: string,
  ): Promise<Response> {
    try {
      let find: any = {};

      if (industry_type_id) {
        const industryType = await this.industryTypesService.findOne(
          {
            industry_type_id,
          },
          false,
          { id: true },
        );

        if (!industryType) {
          throw new NotFoundException('Industry type not found');
        }

        find.industry_types = industryType;
      }

      const data = await this.industryAppProcessService.find(find);

      return response.send({
        status: true,
        message: 'Industry Types Process fetched successfully',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

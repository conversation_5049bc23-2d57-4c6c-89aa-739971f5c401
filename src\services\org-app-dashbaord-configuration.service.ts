import {
  HttpStatus,
  HttpException,
  Injectable,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OrgAppConfigurationDto } from 'src/dto/org-app-configuration.dto';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Organization } from 'src/entities/organization.entity';
import { generateUUID } from 'src/util';
import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  Repository,
} from 'typeorm';
import { AppsService } from './apps.service';
import { OrganizationService } from './organization.service';
import {
  OrgAppDashboardConfigurationRepository,
  OrgAppDashboardConfigurationRepositoryDocument,
} from 'src/entities/mongodb/org-app-dashboard-configuration.enity';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { OrgAppDashboardConfigurationModule } from 'src/controllers/org-app-dashboard-configuration/org-app-dashboard-configuration.module';
import {
  CreateOrgAppDbConfigDto,
  UpdateOrgAppDbConfigDto,
} from 'src/dto/org-app-dashboard-configuration.dto';
import { OrgFormsRepositoryService } from './orgformsrepository.service';
import { faker } from '@faker-js/faker';

@Injectable()
export class OrgAppDashboardConfigurationService {
  constructor(
    @InjectModel(OrgAppDashboardConfigurationRepository.name)
    private readonly OrgAppDbConfigRepo: Model<OrgAppDashboardConfigurationRepositoryDocument>,

    private readonly orgFormsRepositoryService: OrgFormsRepositoryService,
  ) {}

  /**
   * @variable select
   */
  public select: QueryOptions<OrgAppDashboardConfigurationRepository> = {
    id: false,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create Organization App Dashboard Configuration
   * @param createOrgAppDbConfigDto CreateOrgAppDbConfigDto
   * @returns Promise<OrgAppDashboardConfigurationRepository>
   */
  async create(
    app_id: string,
    organization_id: string,
    createOrgAppDbConfigDto: CreateOrgAppDbConfigDto,
  ): Promise<OrgAppDashboardConfigurationRepository> {
    try {
      const form = await this.orgFormsRepositoryService.findOne(
        {
          form_id: createOrgAppDbConfigDto.form_id,
        },
        false,
        { id: true },
      );

      if (!form) {
        throw new BadRequestException('Invalid form id');
      }

      return await this.OrgAppDbConfigRepo.create({
        ...createOrgAppDbConfigDto,
        app_id,
        organization_id,
        form,
        configuration_id: generateUUID(),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update Organization App Dashboard Configuration
   * @param updateOrgAppDbConfigDto UpdateOrgAppDbConfigDto
   * @returns Promise<OrgAppDashboardConfigurationRepository>
   */
  async edit(
    configuration_id: string,
    updateOrgAppDbConfigDto: UpdateOrgAppDbConfigDto,
  ): Promise<OrgAppDashboardConfigurationRepository> {
    try {
      return await this.update(configuration_id, updateOrgAppDbConfigDto);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  //Update the record
  async update(
    configuration_id: string,
    body: any,
  ): Promise<OrgAppDashboardConfigurationRepository> {
    try {
      return await this.OrgAppDbConfigRepo.findOneAndUpdate(
        {
          configuration_id,
        },
        body,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find app dashboard configuration record by condition
   * @param condition Object "{column: value}"
   * @returns Promise<OrgAppDashboardConfigurationRepository>
   */
  async findOne(
    condition: QueryOptions<OrgAppDashboardConfigurationRepository>,
    withDeleted = false,
    select: QueryOptions<OrgAppDashboardConfigurationRepository> = this.select,
    expand = 'form',
  ): Promise<OrgAppDashboardConfigurationRepository> {
    try {
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }

      let query = this.OrgAppDbConfigRepo.findOne(condition);

      if (expand) {
        query = query.populate(expand);
      }

      return await query.select(select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Organization App Dashboard Configuration Preview Details By App Id
   * @param condition Object "{column: value}"
   * @returns Promise<OrgAppDashboardConfigurationRepository>
   */
  async previewDetailsByAppId(
    condition: QueryOptions<OrgAppDashboardConfigurationRepository>,
  ): Promise<any> {
    try {
      const configurationDetails = await this.findOne(condition);

      if (!configurationDetails) {
        throw new BadRequestException('App Dashboard Configuration not found');
      }

      const formSections = configurationDetails.form.fields;
      const listingDetails = configurationDetails.listing_details;

      const fakeData = [];

      for (let i = 0; i < 10; i++) {
        const record = {};

        for (const [key, fieldId] of Object.entries(listingDetails)) {
          const field = this.findFieldById(formSections, fieldId);

          if (field) {
            record[key] = this.generateFakeValue(field);
          }
        }

        fakeData.push(record);
      }

      return fakeData;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private findFieldById(sections: any, fieldId: any): any {
    for (const sectionKey in sections) {
      const section = sections[sectionKey];
      for (const field of section.fields) {
        if (field.field_id === fieldId) {
          return field;
        }
      }
    }
    return null;
  }

  private generateFakeValue(field: any): any {
    switch (field.input_type || field.type) {
      case 'text':
        return faker.person.firstName();
      case 'date':
        return faker.date.past().toISOString().split('T')[0];
      case 'textarea':
        return faker.location.streetAddress();
      case 'paragraph':
        return field.label; // If the label is used as text
      default:
        return 'Unknown Field Type';
    }
  }
}

import {
  BUTTON_STYLE,
  MOBILE_LAYOUT,
  SECTION_VIEW_TYPE,
  TABLET_LAYOUT,
} from './interfaces/theme.interface';

export const INPUT_TYPES_REGEX = {
  email: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/,
  // password: /^[\w]{8,}$/,
  // text: /^[\w]{3,}$/,
  number: /^[\d]+$/,
};

export const INPUTS = [
  {
    name: 'Text',
    type: 'input',
    label: 'Text',
    input_type: 'text',
    options: [],
    attributes: [],
    input_class: 'input',
  },
  {
    name: 'Number',
    type: 'input',
    label: 'Number',
    input_type: 'number',
    input_class: 'input',
    options: [],
    attributes: [],
  },
  {
    name: 'Large Text',
    type: 'textarea',
    label: 'Large Text',
    input_type: null,
    options: [],
    attributes: [],
    input_class: 'input',
  },
  {
    name: '<PERSON><PERSON>',
    type: 'input',
    label: 'Email',
    input_type: 'email',
    options: [],
    attributes: [],
    input_class: 'input',
  },
  {
    name: 'Dropdown',
    type: 'select',
    label: 'Dropdown',
    input_type: null,
    options: [
      {
        label: 'Option1',
        value: 'Option1',
      },
    ],
    attributes: [],
    input_class: 'input',
  },
  {
    name: 'Checkbox',
    type: 'input',
    label: 'Checkbox',
    input_type: 'checkbox',
    options: [
      {
        label: 'Option1',
        value: 'Option1',
      },
    ],
    attributes: [],
    input_class: 'input',
  },
  {
    name: 'Multiple Choice',
    type: 'input',
    label: 'Multiple Choice',
    input_type: 'radio',
    options: [
      {
        label: 'Option1',
        value: 'Option1',
      },
    ],
    attributes: [],
    input_class: 'input',
  },
  {
    name: 'Date',
    type: 'input',
    label: 'Date',
    input_type: 'date',
    options: [],
    attributes: [],
    input_class: 'input',
  },
];

export const getFormField = (column) => {
  let attr = '';
  if (column?.attributes) {
    attr = column?.attributes
      .map((attr) => `${attr.attr_type}="${attr.attr_value}"`)
      .join(' ');
  }
  switch (column.type) {
    case 'input':
      return `<label>${column.label}</label>
  <input type="${column.input_type}" name="${column.name}" class="${column.input_class
        }"  required=${column.is_required} ${attr} ${column?.value ? "value='" + column?.value + "'" : ''
        }  />`;
    case 'select':
      return `<label>${column.label}</label>
      <select name="${column.name}" class="${column.input_class}"  required=${column.is_required
        } ${attr} >
        ${column.options.map(
          (option) =>
            `<option value="${option.value}" ${column?.value === option.value ? 'selected' : ''
            }>${option.label}</option>`,
        )}
      </select>`;
  }
};

export const appThemes: any = (app_process: string) => {
  switch (app_process) {
    case 'HC_OBRD':
      return {
        textColor: '#000000',
        bodyFont: 'roboto',
        titleFont: 'roboto',
        linkColor: '#0276cb',
        buttonColor: '#0F64A1',
        backgroundColor: '#F0F2F4',
        buttonTextColor: '#FFFFFF',
        navigationColor: '#2B81BF',
        navigationTextColor: '#FFFFFF',
        mobileLayout: MOBILE_LAYOUT.singleColumn,
        tabletLayout: TABLET_LAYOUT.doubleColumn,
        buttonStyle: BUTTON_STYLE.standard,
        sectionViewType: SECTION_VIEW_TYPE.plain,
      };

    default:
      return {
        textColor: '#000000',
        bodyFont: 'roboto',
        titleFont: 'roboto',
        linkColor: '#26BBFA',
        buttonColor: '#F47B20',
        backgroundColor: '#ffffff',
        buttonTextColor: '#ffffff',
        navigationColor: '#d4d4d4',
        navigationTextColor: '#000000',
        mobileLayout: MOBILE_LAYOUT.singleColumn,
        tabletLayout: TABLET_LAYOUT.doubleColumn,
        buttonStyle: BUTTON_STYLE.standard,
        sectionViewType: SECTION_VIEW_TYPE.plain,
      };
  }
};


export const REQUEST_CONTEXT = '_requestContext';
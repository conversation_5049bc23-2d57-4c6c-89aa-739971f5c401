import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, tap } from 'rxjs';
import { Request } from 'express';

type RequestContext = {
  user: Request['user'];
  query: Request['query'];
  params: Request['params'];
  body: Request['body'];
};

@Injectable()
export class RequestContextService {
  private static request: RequestContext;

  static setRequest(request: RequestContext | undefined): void {
    RequestContextService.request = request;
  }
  static getRequest(): RequestContext | undefined {
    return RequestContextService.request;
  }
}

export class InjectRequestInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    RequestContextService.setRequest({
      user: request.user,
      query: request.query,
      params: request.params,
      body: request.body,
    });
    return next.handle().pipe(
      tap(() => {
        RequestContextService.setRequest(undefined);
      }),
    );
  }
}

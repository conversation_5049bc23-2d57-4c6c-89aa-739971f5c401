import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport/dist/auth.guard';
import { OnBoardOrgChecklistRepoDto } from 'src/dto/onboard-org-checklist.dto';
import { OnBoardOrgChecklistRepository } from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { Request, Response } from 'express';
import { AppService } from 'src/app.service';
import { OrganizationService } from 'src/services/organization.service';
import { AppsService } from 'src/services/apps.service';
import { QueryOptions } from 'mongoose';
@Controller('onboard-org-checklist')
export default class OnBoardOrgChecklistController {
  constructor(
    private readonly OnBoardOrgChecklistService: OnBoardOrgChecklistService,
    private readonly appService: AppsService,
    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Create OnBoardOrgChecklist
   * @param OnBoardOrgChecklistRepoDto OnBoardOrgChecklistRepoDto
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async create(
    @Body() OnBoardOrgChecklistRepoDto: OnBoardOrgChecklistRepoDto,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: OnBoardOrgChecklistRepository =
        await this.OnBoardOrgChecklistService.create(
          OnBoardOrgChecklistRepoDto,
          request.user['organization_id'],
        );

      return response
        .status(data ? HttpStatus.CREATED : HttpStatus.CONFLICT)
        .send({
          status: data ? true : false,
          message: data
            ? 'Item added to the checklist successfully!'
            : 'Unable to create the Onboard Employee checklist.',
        });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update OnBoardOrgChecklist
   * @param checklist_id string
   * @param OnBoardOrgChecklistRepoDto OnBoardOrgChecklistRepoDto
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Put('/:checklist_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async update(
    @Param('checklist_id') checklist_id: string,
    @Body() OnBoardOrgChecklistRepoDto: OnBoardOrgChecklistRepoDto,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: OnBoardOrgChecklistRepository =
        await this.OnBoardOrgChecklistService.update(
          checklist_id,
          OnBoardOrgChecklistRepoDto,
        );

      return data
        ? response.status(HttpStatus.OK).send({
            status: true,
            message: 'Item updated successfully!',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to update Onboard Employee checklist',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Onboard onboarding_employee checklist by id
   * @param checklist_id string
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Get()
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async findOne(
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const organization_id = request.user['organization_id'];

      if (!organization_id) {
        throw new UnauthorizedException('Invalid organization.');
      }

      const data: OnBoardOrgChecklistRepository =
        await this.OnBoardOrgChecklistService.findOne({
          organization_id,
        });

      return response.status(HttpStatus.OK).send({
        status: true,
        data,
        message: 'Onboard Employee checklist fetched successfully .',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // /**
  //  * Get OnBoardOrgChecklists
  //  * @param request Request
  //  * @param response Response
  //  * @returns Promise<Response>
  //  */
  // @Get('')
  // @UsePipes(ValidationPipe)
  // @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  // async find(
  //   @Query('app_id') app_id: string,
  //   @Req() request: Request,
  //   @Res() response: Response,
  // ): Promise<Response> {
  //   try {
  //     const app = await this.appService.findOne({ app_id }, false, {
  //       id: true,
  //     });

  //     if (!app) {
  //       throw new HttpException('App not found', HttpStatus.NOT_FOUND);
  //     }

  //     const organization = await this.organizationService.findOne(
  //       {
  //         organization_id: request?.user['organization_id'],
  //       },
  //       false,
  //       { id: true },
  //     );

  //     if (!organization) {
  //       throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
  //     }

  //     const findBy: QueryOptions<OnBoardOrgChecklistRepository> = {
  //       app_id,
  //       organization_id: request?.user['organization_id'],
  //     };

  //     const data: OnBoardOrgChecklistRepository[] =
  //       await this.OnBoardOrgChecklistService.findAll(findBy);

  //     return data
  //       ? response.status(HttpStatus.OK).send({
  //           status: true,
  //           data,
  //           message:
  //             'Onboard onboarding_employee checklist fetched successfully .',
  //         })
  //       : response.status(HttpStatus.CONFLICT).send({
  //           status: false,
  //           message: 'Unable to create the onboarding_employee.',
  //         });
  //   } catch (error) {
  //     throw new HttpException(
  //       error?.message || 'Something went wrong. Please try again later.',
  //       error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }
}

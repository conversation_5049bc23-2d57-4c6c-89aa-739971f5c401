import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  Repository,
  Not,
} from 'typeorm';
import { Caregivers } from '../entities/caregivers.entity';
import { CreateCaregiverDto, UpdateCaregiverDto } from '../dto/caregivers.dto';
import { OrganizationService } from './organization.service';
import { generateBcryptHash, generateUUID } from 'src/util';

@Injectable()
export class CaregiversService {
  constructor(
    @InjectRepository(Caregivers, 'mysql')
    private caregiversRepo: Repository<Caregivers>,
    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,
  ) {}

  public select: FindOptionsSelect<Caregivers> = {
    caregiver_id: true,
    name: true,
    email: true,
    mobile_number: true,
    clients: true,
    id: false,
    password: false,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create a new caregiver scoped to an organization
   * @param dto CreateCaregiverDto
   * @param organization_id number (from authenticated user)
   * @returns Promise<Caregivers>
   */
  async create(
    dto: CreateCaregiverDto,
    organization_id: string,
  ): Promise<Caregivers> {
    try {
      const organization = await this.organizationService.findOne(
        { organization_id },
        false,
        { id: true },
      );

      if (!organization) {
        throw new NotFoundException(
          `Organization with ID ${organization_id} not found`,
        );
      }

      // Check for existing caregiver with same email
      const existingEmailCaregiver = await this.findOne(
        {
          email: dto.email,
          organization,
        },
        false,
        { id: true },
      );

      if (existingEmailCaregiver) {
        throw new HttpException(
          'Email already attached with another caregiver',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Check for existing caregiver with same mobile number
      const existingMobileCaregiver = await this.findOne(
        {
          mobile_number: dto.mobile_number,
          organization,
        },
        false,
        { id: true },
      );

      if (existingMobileCaregiver) {
        throw new HttpException(
          'Mobile number already attached with another caregiver',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.caregiversRepo.save({
        ...dto,
        password: await generateBcryptHash(dto.password),
        caregiver_id: `crg_${generateUUID()}`,
        organization,
      });
    } catch (error) {
      console.error('Error creating caregiver:', error);
      throw new InternalServerErrorException('Failed to create caregiver');
    }
  }

  /**
   * Get all caregivers for a specific organization
   * @param organization_id number
   * @returns Promise<Caregivers[]>
   */
  async findAll(
    findBy: FindOptionsWhere<Caregivers>,
    withDeleted = false,
    select = this.select,
    relations: FindOptionsRelations<Caregivers> = {},
  ): Promise<Caregivers[]> {
    try {
      return await this.caregiversRepo.find({
        where: findBy,
        withDeleted,
        relations,
        select,
      });
    } catch (error) {
      console.error('Error fetching caregivers:', error);
      throw new InternalServerErrorException('Failed to fetch caregivers');
    }
  }

  /**
   * Get a single caregiver by ID scoped to an organization
   * @param id number
   * @param organization_id number
   * @returns Promise<Caregivers>
   */
  async findOne(
    findBy: FindOptionsWhere<Caregivers>,
    withDeleted = false,
    select = this.select,
    relations: FindOptionsRelations<Caregivers> = {},
  ): Promise<Caregivers> {
    try {
      return await this.caregiversRepo.findOne({
        where: findBy,
        withDeleted,
        relations,
        select,
      });
    } catch (error) {
      console.error('Error finding caregiver:', error);
      throw new InternalServerErrorException('Failed to find caregiver');
    }
  }

  /**
   * Update a caregiver by ID, scoped to an organization
   * @param caregiver_id string
   * @param dto UpdateCaregiverDto
   * @returns Promise<Caregivers>
   */
  async update(caregiver_id: string, dto: UpdateCaregiverDto): Promise<any> {
    const existingCaregiver: Caregivers = await this.findOne(
      { caregiver_id },
      false,
      {
        ...this.select,
        id: true,
      },
      { organization: true },
    );

    if (!existingCaregiver) {
      throw new HttpException('Caregiver Not Found', HttpStatus.NOT_FOUND);
    }

    // Check if email is being updated and if it already exists for another caregiver
    if (dto?.email && dto?.email !== existingCaregiver.email) {
      const duplicateEmailCaregiver = await this.caregiversRepo.findOne({
        where: {
          email: dto.email,
          organization: { id: existingCaregiver?.organization?.id },
          caregiver_id: Not(caregiver_id), // Exclude current caregiver
        },
      });

      if (duplicateEmailCaregiver) {
        throw new HttpException(
          'Email already attached with another caregiver',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // Check if mobile number is being updated and if it already exists for another caregiver
    if (
      dto?.mobile_number &&
      dto?.mobile_number !== existingCaregiver.mobile_number
    ) {
      const duplicateMobileCaregiver = await this.caregiversRepo.findOne({
        where: {
          mobile_number: dto.mobile_number,
          organization: { id: existingCaregiver?.organization?.id },
          caregiver_id: Not(caregiver_id), // Exclude current caregiver
        },
      });

      if (duplicateMobileCaregiver) {
        throw new HttpException(
          'Mobile number already attached with another caregiver',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // Hash password if it's being updated
    if (dto?.password) {
      dto.password = await generateBcryptHash(dto.password);
    }

    return await this.caregiversRepo.save({ ...existingCaregiver, ...dto });
  }

  /**
   * Soft-delete a caregiver by ID, scoped to an organization
   * @param id number
   * @param organization_id number
   * @returns Promise<void>
   */
  async remove(caregiver_id: string): Promise<void> {
    try {
      const caregiver = await this.findOne({ caregiver_id });
      if (!caregiver) {
        throw new HttpException('Caregiver Not Found', HttpStatus.NOT_FOUND);
      }

      await this.caregiversRepo.softDelete({ caregiver_id });
    } catch (error) {
      console.error('Error deleting caregiver:', error);
      throw new InternalServerErrorException('Failed to delete caregiver');
    }
  }
}

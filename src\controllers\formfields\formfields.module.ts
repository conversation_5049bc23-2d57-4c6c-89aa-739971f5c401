import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>FieldsController } from './formfields.controller';
import {
  FormFields,
  FormFieldsSchema,
} from 'src/entities/mongodb/formfields.entity';
import { FormFieldsService } from 'src/services/formfields.service';
import { MongooseModule } from '@nestjs/mongoose';
import { FieldExistRule } from 'src/util/validations/forms.validations';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: FormFields.name, schema: FormFieldsSchema },
    ]),
  ],
  exports: [FormFieldsService],
  providers: [FormFieldsService, FieldExistRule],
  controllers: [FormFieldsController],
})
export class FormFieldsModule {}

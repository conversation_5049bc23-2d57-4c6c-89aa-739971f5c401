import {
  Body,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Controller,
  NotFoundException,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response } from 'express';
import { QueryOptions } from 'mongoose';
import { find } from 'rxjs';
import { FormFieldsDto } from 'src/dto/formrepository.dto';
import { FormFields } from 'src/entities/mongodb/formfields.entity';
import { FormFieldsService } from 'src/services/formfields.service';
import { FindOptionsWhere } from 'typeorm';

@Controller('form-fields')
export class FormFieldsController {
  constructor(private frService: FormFieldsService) {}

  /**
   * Create Form Field
   * @param body FormFieldDto
   * @returns Promise<Response>
   */
  @Post('/')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'))
  async create(
    @Body() body: FormFieldsDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.frService.create(body);
      return data
        ? response.send({
            status: true,
            message: 'Field created Successfully',
            data: data,
          })
        : response
            .status(500)
            .send({ status: false, message: 'Unable to create the field' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get all form fields
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/')
  @UseGuards(AuthGuard('jwt'))
  async getList(
    @Res() response: Response,
    @Query('quiz_form_fields') quiz_form_fields: boolean,
  ): Promise<Response> {
    try {
      const findBy: QueryOptions<FormFields> = {
        status: true,
        is_quiz_field: { $ne: true },
      };

      if (quiz_form_fields) {
        findBy.is_quiz_field = true;
      }

      const list = await this.frService.list(findBy);

      return response.send({
        status: true,
        message: 'Fields get successfully',
        data: list,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get field by id
   * @param field_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/:field_id')
  @UseGuards(AuthGuard('jwt'))
  async getForm(
    @Param('field_id') field_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.frService.findOne({ field_id });
      if (!data) {
        throw new NotFoundException();
      }
      return response.send({ status: true, data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the field by field_id
   * @param body FormFieldsDto
   * @param field_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Put('/:field_id')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(ValidationPipe)
  async updateForm(
    @Body() body: FormFieldsDto,
    @Param('field_id') field_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.frService.update(body, field_id);
      return data
        ? response.send({
            status: true,
            message: 'Field updated Successfully',
            data: data,
          })
        : response.status(500).send({
            status: false,
            message: 'Unable to update the field',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

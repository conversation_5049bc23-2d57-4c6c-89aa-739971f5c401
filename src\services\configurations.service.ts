import {
  HttpStatus,
  HttpException,
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  In,
  Not,
  Repository,
} from 'typeorm';

import { UserDto, UserRegisterDto } from 'src/dto/user.dto';
import { User } from 'src/entities/user.entity';
import { generateBcryptHash, generateUUID } from 'src/util';
import { AppsService } from './apps.service';
import { OrgFormsRepository } from 'src/entities/mongodb/orgformsrepository.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { ConfigurationDto } from 'src/dto/configuration.dto';
import { Organization } from 'src/entities/organization.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios/dist';
import * as FormData from 'form-data';
import * as moment from 'moment';
import { Promise } from 'mongoose';
import { google, drive_v3, Auth } from 'googleapis';
import axios from 'axios';
import { ClientsRepositoryService } from './clientsrepository.service';
import { fa } from '@faker-js/faker';
const { Readable } = require('stream');

@Injectable()
export class ConfigurationsService {
  private drive: drive_v3.Drive;
  private auth: Auth.OAuth2Client;

  constructor(
    @InjectRepository(Configurations, 'mysql')
    private ConfigurationsRepository: Repository<Configurations>,

    @InjectRepository(Organization, 'mysql')
    private organizationRepository: Repository<Organization>,

    private httpService: HttpService,

    private readonly clientsRepositoryService: ClientsRepositoryService,
  ) {}

  /**
   * @variable select FindOptionsSelect<Configurations>
   */

  public select: FindOptionsSelect<Configurations> = {
    id: false,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create Configuration Details
   * @param configurationDto ConfigurationDto
   * @returns Promise<ConfigurationRepository>
   */
  async create(configurationDto: ConfigurationDto): Promise<Configurations> {
    try {
      const organization = await this.getOrganization(
        configurationDto.organization_id,
      );

      const configuration = await this.findOne(
        {
          type: configurationDto.type,
          organization,
        },
        false,
        null,
        { organization: true },
      );

      if (configuration) {
        throw new BadRequestException(
          `Configuration of type ${configurationDto.type} already exists for the given organization.Please Update it`,
        );
      }

      return await this.ConfigurationsRepository.save({
        ...configurationDto,
        organization: await this.getOrganization(
          configurationDto.organization_id,
        ),
        configuration_id: generateUUID(),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * update operation
   * @param body ThemesDto
   * @param theme_id string
   * @param organization_id string
   * @returns Promise<Boolean>
   */
  async update(
    configuration_id: string,
    body: ConfigurationDto,
    organization_id: string,
  ): Promise<any> {
    try {
      const organization = await this.getOrganization(organization_id);

      const data = await this.findOne(
        { configuration_id, organization },
        false,
        {
          ...this.select,
          id: true,
          name: true,
          type: true,
          details: true,
        },
      );
      if (!data)
        throw new HttpException(
          'Configuration details not found',
          HttpStatus.NOT_FOUND,
        );

      return await this.ConfigurationsRepository.save({
        id: data.id,
        ...body,
        details: {
          ...data.details,
          ...body.details,
        },
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Return Organization
   * @parma organization_id string
   * @return Promise<Organization>
   */
  async getOrganization(organization_id: string): Promise<Organization> {
    const data = await this.organizationRepository.findOne({
      where: { organization_id },
      withDeleted: false,
      select: { id: true },
    });
    if (data) return data;
    throw new NotFoundException('Organization not found');
  }

  /**
   * Get Configuration record by condition
   * @param findBy Object "{column: value}"
   * @returns Promise<Configurations>
   */
  async findOne(
    findBy: FindOptionsWhere<Configurations>,
    withDeleted = false,
    select: FindOptionsSelect<Configurations> = this.select,
    relations: FindOptionsRelations<Configurations> = {},
  ): Promise<Configurations> {
    try {
      return await this.ConfigurationsRepository.findOne({
        select,
        relations,
        withDeleted,
        where: findBy,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Configurations record by condition
   * @param findBy Object "{column: value}"
   * @returns Promise<Configurations>
   */
  async findAll(
    findBy: FindOptionsWhere<Configurations>,
    withDeleted = false,
    select: FindOptionsSelect<Configurations> = this.select,
    relations: FindOptionsRelations<Configurations> = {},
  ): Promise<Configurations[]> {
    try {
      return await this.ConfigurationsRepository.find({
        select,
        relations,
        withDeleted,
        where: findBy,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAuthToken(organization_id: string, code: string, message?: string) {
    try {
      const organization = await this.getOrganization(organization_id);

      const configurationDetails = await this.findOne(
        {
          type: 'storage',
          organization,
        },
        false,
        null,
        { organization: true },
      );

      if (configurationDetails?.name == 'one_drive') {
        const url = `https://login.microsoftonline.com/${configurationDetails?.details?.tenentId}/oauth2/v2.0/token`;

        const data = new FormData();

        data.append('grant_type', 'authorization_code');

        data.append(
          'client_secret',
          configurationDetails?.details?.secretValue,
        );

        data.append('client_id', configurationDetails?.details?.clientId);

        data.append(
          'redirect_uri',
          configurationDetails?.details?.backendRedirectUrl,
        );

        data.append('code', code);

        try {
          const response = await axios.post(url, data);

          if (response.status) {
            await this.ConfigurationsRepository.save({
              id: configurationDetails.id,
              ...configurationDetails,
              details: {
                ...configurationDetails.details,
                authToken: response.data?.access_token,
                refreshToken: response.data?.refresh_token,
                tokenExpire: moment()
                  .add(response.data?.expires_in, 'seconds')
                  .format(),
              },
            });
            return true;
          } else {
            return false;
          }
        } catch (error) {
          throw new HttpException(
            error?.message || 'Something went wrong. Please try again later.',
            error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      } else if (configurationDetails?.name == 'google_drive') {
        try {
          await this.setGoogleKeysAndConfiguration(configurationDetails);

          const { tokens } = await this.auth.getToken(code);

          await this.ConfigurationsRepository.save({
            id: configurationDetails.id,
            ...configurationDetails,
            details: {
              ...configurationDetails.details,
              accessToken: tokens.access_token,
              refreshToken: tokens.refresh_token,
              tokenExpire: tokens.expiry_date?.toString(),
            },
          });

          return true;
        } catch (error) {
          throw new HttpException(
            error?.message || 'Something went wrong. Please try again later.',
            error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getFoldersFromSelectedDrive(organization_id: string, search: string) {
    const returnObj = {
      status: true,
      isExisted: false,
    };

    const organization = await this.getOrganization(organization_id);

    const configurationDetails = await this.findOne(
      {
        type: 'storage',
        organization,
      },
      false,
      null,
      { organization: true },
    );

    if (!configurationDetails) {
      throw new HttpException(
        {
          message: 'First please add the configurations',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (configurationDetails?.name == 'one_drive') {
      // let url = `https://graph.microsoft.com/v1.0/drives/${configurationDetails?.details?.drive_id}/items/${configurationDetails?.details?.folder_id}/search(q='${search}')`;
      let url = `https://graph.microsoft.com/v1.0/drives/${configurationDetails?.details?.drive_id}/items/${configurationDetails?.details?.folder_id}/children`;

      try {
        let result = [];

        while (url) {
          const response = await this.httpService.axiosRef.get(url, {
            headers: {
              Authorization: `Bearer ${configurationDetails.details?.authToken}`,
            },
          });

          const data = response.data;
          url = data['@odata.nextLink'] || null;
          result = [...result, ...data?.value];
        }

        if (result.length > 0) {
          const existedFolders = result
            ?.filter((value: any) => !('file' in value))
            ?.filter(
              (value: any) =>
                value?.name?.toLowerCase() === search?.toLowerCase(),
            );
          if (existedFolders.length > 0) {
            returnObj.isExisted = true;
          }
          return returnObj;
        } else {
          return returnObj;
        }
      } catch (error) {
        throw error?.response?.data?.error || error;
      }
    } else if (configurationDetails?.name == 'google_drive') {
      await this.setGoogleKeysAndConfiguration(configurationDetails);

      const response = await this.drive.files.list({
        q: `mimeType='application/vnd.google-apps.folder' and name='${search}' and '${configurationDetails?.details?.folderId}' in parents`,
        fields: 'files(id, name)',
      });

      const files = response.data.files;

      if (files.length > 0) {
        returnObj.isExisted = true;

        return returnObj;
      } else {
        return returnObj;
      }
    }
  }

  /**
   * Generate Login link
   * @param organizationId: string"
   * @returns Promise<>
   */
  async generateLoginLink(organizationId: string) {
    try {
      let loginLink: string;

      const organization = await this.getOrganization(organizationId);

      const configurationDetails = await this.findOne(
        {
          type: 'storage',
          organization,
        },
        false,
        null,
        { organization: true },
      );

      if (!configurationDetails) {
        throw new NotFoundException(
          'First please add the storage configuration from Storage Configuration',
        );
      }

      if (configurationDetails?.name == 'one_drive') {
        loginLink = `https://login.microsoftonline.com/${configurationDetails?.details?.tenentId}/oauth2/v2.0/authorize?client_id=${configurationDetails?.details?.clientId}&scope=files.readwrite.all offline_access&response_type=code&redirect_uri=${configurationDetails?.details?.backendRedirectUrl}`;
      } else if (configurationDetails?.name == 'google_drive') {
        loginLink = await this.generateGoogleLoginLink(configurationDetails);
      } else {
        throw new BadRequestException(
          'Something went wrong. Please try again later.',
        );
      }

      return loginLink;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Check and assign the OAuth & Drive Client to their respective variables
   */
  /**
   * Assign the Google OAuth Client to auth variable & Assign the drive client to drive variable
   * If refresh_token was present then set the credentials as refresh_token to OAuth Client
   * @param refresh_token?
   */
  async setGoogleKeysAndConfiguration(configurationDetails: Configurations) {
    try {
      this.auth = new google.auth.OAuth2(
        configurationDetails?.details?.clientId,
        configurationDetails?.details?.secretKey,
        configurationDetails?.details?.backendRedirectUrl,
      );

      if (configurationDetails?.details?.refreshToken) {
        this.auth.setCredentials({
          refresh_token: configurationDetails?.details?.refreshToken,
        });
      }

      this.drive = google.drive({
        version: 'v3',
        auth: this.auth,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Generate the Google login link for access the application
   * @returns Promise<string>
   */
  async generateGoogleLoginLink(
    configurationDetails: Configurations,
  ): Promise<string> {
    try {
      await this.setGoogleKeysAndConfiguration(configurationDetails);

      return this.auth.generateAuthUrl({
        access_type: 'offline',
        scope: ['https://www.googleapis.com/auth/drive'],
        prompt: 'consent',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Create Folder inside the Auth. Google drive Account
   * @param name
   * @param parent_id string
   * @returns Promise<any>
   */
  async createFolder(
    organization_id: string,
    name: string,
    parent_id = null,
  ): Promise<any> {
    try {
      const organization = await this.getOrganization(organization_id);

      const configurationDetails = await this.findOne(
        {
          name: 'google_drive',
          type: 'storage',
          organization,
        },
        false,
        null,
        { organization: true },
      );

      if (!configurationDetails) {
        throw new NotFoundException(
          'Google Drive Configuration Details Not Found',
        );
      }

      await this.setGoogleKeysAndConfiguration(configurationDetails);

      const params: drive_v3.Params$Resource$Files$Create = {
        requestBody: {
          name,
          mimeType: 'application/vnd.google-apps.folder',
        },
        fields: 'id,name',
      };
      if (parent_id) {
        params.requestBody.parents = [parent_id];
      }

      return this.drive.files.create(params).catch((err) => {
        throw new HttpException(
          {
            message: err.message,
          },
          HttpStatus.BAD_REQUEST,
        );
      });
    } catch (err) {
      throw new HttpException(
        {
          message: err.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Get the folders inside the Auth. Google/One Drive
   * @returns Promise<any>
   */
  async getFoldersList(organization_id: string): Promise<any> {
    try {
      const organization = await this.getOrganization(organization_id);

      const configurationDetails = await this.findOne(
        {
          type: 'storage',
          organization,
        },
        false,
        null,
        { organization: true },
      );

      if (!configurationDetails) {
        throw new NotFoundException('Configuration Details Not Found');
      }

      if (configurationDetails?.name == 'one_drive') {
        try {
          const url1 = `https://graph.microsoft.com/v1.0/me/drive/sharedWithMe/`;
          const url = `https://graph.microsoft.com/v1.0/me/drive/root/children`;

          const foldersList = [];

          const response = await axios.get(url, {
            headers: {
              Authorization: `Bearer ${configurationDetails?.details?.authToken}`,
            },
          });

          if (response?.data) {
            response?.data?.value.forEach((item: any) => {
              if (item?.folder) {
                foldersList.push({
                  folder_name: item.name,
                  item_id: item.id,
                  drive_id: item?.parentReference?.driveId,
                });
              }
            });
          }

          const sharedResponse = await axios.get(url1, {
            headers: {
              Authorization: `Bearer ${configurationDetails?.details?.authToken}`,
            },
          });

          if (sharedResponse?.data) {
            sharedResponse?.data?.value.forEach((item: any) => {
              if (item?.folder) {
                foldersList.push({
                  folder_name: `${item.name}`,
                  item_id: item.id,
                  drive_id: item?.remoteItem?.parentReference?.driveId,
                });
              }
            });
          }

          return {
            status: true,
            message: 'Folders list fetched successfully',
            type: 'one_drive',
            foldersList: foldersList.map((folder) => {
              folder.isSelected =
                folder.item_id ===
                configurationDetails.details.one_drive_folder_id;
              return folder;
            }),
          };
        } catch (error) {
          throw new HttpException(
            error?.message || 'Something went wrong. Please try again later.',
            error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      } else if (configurationDetails?.name == 'google_drive') {
        try {
          await this.setGoogleKeysAndConfiguration(configurationDetails);

          return {
            status: true,
            message: 'Folders list fetched successfully',
            type: 'google_drive',
            foldersList:
              (
                await this.drive.files.list({
                  q: `mimeType='application/vnd.google-apps.folder' and ( 'me' in owners or sharedWithMe )`,
                  fields: 'files(id, name)',
                })
              )?.data?.files || [],
          };
        } catch (error) {
          throw new HttpException(
            error?.message || 'Something went wrong. Please try again later.',
            error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      }
    } catch (err) {
      throw new HttpException(
        {
          message: err.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Delete Folder inside the Auth. Google drive Account by its filed
   * @param fileId
   * @returns Promise<any>
   */
  async deleteFolder(organization_id: string, folderId: string): Promise<any> {
    try {
      const organization = await this.getOrganization(organization_id);

      const configurationDetails = await this.findOne(
        {
          name: 'google_drive',
          type: 'storage',
          organization,
        },
        false,
        null,
        { organization: true },
      );

      if (!configurationDetails) {
        throw new NotFoundException(
          'Google Drive Configuration Details Not Found',
        );
      }

      await this.setGoogleKeysAndConfiguration(configurationDetails);

      return this.drive.files
        .delete({
          fileId: folderId,
        })
        .catch((err) => {
          throw new HttpException(
            {
              message: err.message,
            },
            HttpStatus.BAD_REQUEST,
          );
        });
    } catch (err) {
      throw new HttpException(
        {
          message: err.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async bufferToStream(buffer) {
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null);
    return stream;
  }

  async uploadToStorage(
    organization_id: string,
    file: any,
    client_id: string,
    folder_name: string,
    filename: string,
    mimetype: string,
  ) {
    try {
      const organization = await this.getOrganization(organization_id);

      const configurationDetails: any = await this.findOne(
        {
          type: 'storage',
          organization,
        },
        false,
        null,
        { organization: true },
      );

      if (!configurationDetails) {
        throw new NotFoundException(
          'First please add the storage configuration from Storage Configuration',
        );
      }

      const client: any = await this.clientsRepositoryService.findOne(
        { client_id },
        false,
        {
          _id: true,
          name: true,
          storage_folder_id: true,
          storage_folder_name: true,
          storage_location: true,
        },
      );

      if (configurationDetails?.name == 'one_drive') {
        const url = `https://graph.microsoft.com/v1.0/drives/${configurationDetails?.details?.drive_id}/items/${configurationDetails?.details?.folder_id}:/${folder_name}/${filename}:/content`;

        try {
          const response = await this.httpService.axiosRef.put(url, file, {
            headers: {
              'Content-Type': mimetype,
              Authorization: `Bearer ${configurationDetails?.details?.authToken}`,
            },
          });

          //Deleting the existing folder
          if (
            client?.storage_location &&
            client?.storage_location == 'one-drive' &&
            folder_name !== client?.storage_folder_name &&
            client?.storage_folder_id
          ) {
            const url = `https://graph.microsoft.com/v1.0/drives/${configurationDetails.details.drive_id}/items/${client?.storage_folder_id}`;

            try {
              await this.httpService.axiosRef.delete(url, {
                headers: {
                  Authorization: `Bearer ${configurationDetails.details.authToken}`,
                },
              });
            } catch (error) {}
          }

          await this.clientsRepositoryService.update(client_id, {
            storage_folder_id: response.data?.parentReference.id,
            storage_folder_name: folder_name,
            storage_location: 'one-drive',
          });
        } catch (err) {
          throw new HttpException(
            {
              message: err.message,
            },
            HttpStatus.BAD_REQUEST,
          );
        }
      } else if (configurationDetails?.name == 'google_drive') {
        await this.setGoogleKeysAndConfiguration(configurationDetails);

        file = await this.bufferToStream(file);

        const params: drive_v3.Params$Resource$Files$Create = {
          requestBody: {
            name: filename,
          },
          media: {
            body: file,
            mimeType: mimetype,
          },
          fields: 'id,name',
        };

        if (configurationDetails?.details?.folderId) {
          if (
            !client?.storage_folder_id ||
            folder_name !== client?.storage_folder_name ||
            client?.storage_location != 'google-drive'
          ) {
            const response = await this.createFolder(
              organization_id,
              folder_name,
              configurationDetails?.details?.folderId,
            );

            if (response && response.data) {
              if (
                client?.storage_folder_id &&
                client?.storage_location == 'google-drive'
              ) {
                try {
                  await this.deleteFolder(
                    organization_id,
                    client?.storage_folder_id,
                  );
                } catch (error) {}
              }

              await this.clientsRepositoryService.update(client_id, {
                storage_folder_id: response.data.id,
                storage_folder_name: folder_name,
                storage_location: 'google-drive',
              });
              params.requestBody.parents = [response.data.id];
            }
          } else {
            params.requestBody.parents = [client?.storage_folder_id];
          }
        }

        const response = await this.drive.files.create(params).catch((err) => {
          throw new HttpException(
            {
              message: err.message,
            },
            HttpStatus.BAD_REQUEST,
          );
        });

        return response;
      } else {
        throw new BadRequestException(
          'Something went wrong. Please try again later.',
        );
      }
    } catch (err) {
      throw new HttpException(
        {
          message: err.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async checkFolder(organization_id: any, path?: string) {
    try {
      return await this.getFoldersFromSelectedDrive(organization_id, path);
    } catch (err) {
      throw new HttpException(
        {
          message: err.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // @Cron(CronExpression.EVERY_5_MINUTES)
  async getOneDriveRefreshToken(organization_id: string) {
    try {
      if (organization_id) {
        const organization = await this.getOrganization(organization_id);

        const oneDrive = await this.findOne(
          {
            name: 'one_drive',
            type: 'storage',
            organization,
          },
          false,
          null,
          { organization: true },
        );

        if (oneDrive) {
          const currentTime = moment();
          const tokenExpire = moment(oneDrive?.details?.tokenExpire);

          if (
            !tokenExpire.isValid() ||
            currentTime.isAfter(tokenExpire.subtract(5, 'minutes'))
          ) {
            const url = `https://login.microsoftonline.com/${oneDrive.details?.tenentId}/oauth2/v2.0/token`;

            const data = new FormData();

            data.append('grant_type', 'refresh_token');

            data.append('client_secret', oneDrive?.details?.secretValue);

            data.append('client_id', oneDrive?.details?.clientId);

            data.append('redirect_uri', oneDrive?.details?.backendRedirectUrl);

            data.append('refresh_token', oneDrive?.details?.refreshToken);

            try {
              const response = await axios.post(url, data);

              await this.ConfigurationsRepository.save({
                id: oneDrive.id,
                ...oneDrive,
                details: {
                  ...oneDrive.details,
                  authToken: response.data?.access_token,
                  refreshToken: response.data?.refresh_token,
                  tokenExpire: moment()
                    .add(response.data?.expires_in, 'seconds')
                    .format(),
                },
              });
            } catch (e) {
              // console.log(e?.response?.data);
              const message =
                e?.response?.data?.error ||
                'Something went wrong please try again later';
            }
          } else {
            console.log('Token has time');
            return true;
          }
        } else {
          console.log({ msg: 'Please do one drive config' });
          return false;
        }
      } else {
        const oneDriveConfigurations = await this.findAll(
          {
            name: 'one_drive',
            type: 'storage',
          },
          false,
          null,
          { organization: true },
        );

        if (oneDriveConfigurations && oneDriveConfigurations.length > 0) {
          oneDriveConfigurations.map(async (oneDriveConfiguration: any) => {
            const currentTime = moment();
            const tokenExpire = moment(
              oneDriveConfiguration?.details?.tokenExpire,
            );

            if (
              !tokenExpire.isValid() ||
              currentTime.isAfter(tokenExpire.subtract(5, 'minutes'))
            ) {
              const url = `https://login.microsoftonline.com/${oneDriveConfiguration.details?.tenentId}/oauth2/v2.0/token`;

              const data = new URLSearchParams();

              data.append('grant_type', 'refresh_token');

              data.append(
                'client_secret',
                oneDriveConfiguration?.details?.secretValue,
              );

              data.append(
                'client_id',
                oneDriveConfiguration?.details?.clientId,
              );

              data.append(
                'redirect_uri',
                oneDriveConfiguration?.details?.backendRedirectUrl,
              );

              data.append(
                'refresh_token',
                oneDriveConfiguration?.details?.refreshToken,
              );

              try {
                const response = await this.httpService.axiosRef.post(
                  url,
                  data,
                  {
                    headers: {
                      'Content-Type': 'application/x-www-form-urlencoded',
                    },
                  },
                );

                await this.ConfigurationsRepository.save({
                  id: oneDriveConfiguration.id,
                  ...oneDriveConfiguration,
                  details: {
                    ...oneDriveConfiguration.details,
                    authToken: response.data?.access_token,
                    refreshToken: response.data?.refresh_token,
                    tokenExpire: moment()
                      .add(response.data?.expires_in, 'seconds')
                      .format(),
                  },
                });
              } catch (e) {
                // console.log(e);
                throw new HttpException(
                  {
                    message: e.message || 'Failed to refresh token',
                  },
                  HttpStatus.BAD_REQUEST,
                );
              }
            }
          });
        }
      }
    } catch (error) {
      throw new HttpException(
        {
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Cron(CronExpression.EVERY_5_HOURS)
  async generateNewTokens() {
    try {
      const configurationDetails = await this.findAll(
        {
          name: 'google_drive',
          type: 'storage',
        },
        false,
        null,
        { organization: true },
      );

      if (configurationDetails && configurationDetails.length) {
        for (const configurationDetail of configurationDetails) {
          await this.processConfiguration(configurationDetail);
        }
      }
    } catch (error) {
      throw new HttpException(
        {
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private async processConfiguration(configurationDetail: any) {
    const currentTime = moment();
    const tokenExpire = moment(
      parseInt(configurationDetail?.details?.tokenExpire, 10),
    );

    if (
      !tokenExpire.isValid() ||
      currentTime.isAfter(tokenExpire.subtract(5, 'minutes'))
    ) {
      console.log('------Google Cron Job-----');
      this.auth = new google.auth.OAuth2(
        configurationDetail?.details?.clientId,
        configurationDetail?.details?.secretKey,
        configurationDetail?.details?.backendRedirectUrl,
      );

      if (configurationDetail?.details?.refreshToken) {
        this.auth.setCredentials({
          refresh_token: configurationDetail?.details?.refreshToken,
        });
      }

      this.drive = google.drive({
        version: 'v3',
        auth: this.auth,
      });
      try {
        const tokens: any = await this.auth.getAccessToken();

        tokens &&
          (await this.ConfigurationsRepository.save({
            id: configurationDetail.id,
            ...configurationDetail,
            details: {
              ...configurationDetail.details,
              accessToken: tokens?.res?.data?.access_token,
              refreshToken:
                tokens?.res?.data?.refresh_token ||
                configurationDetail.details.refreshToken,
              tokenExpire: tokens?.res?.data?.expiry_date?.toString(),
            },
          }));
      } catch (error) {
        throw new HttpException(
          {
            message: error.message,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }
}

import { Schema, Prop, SchemaFactory } from '@nestjs/mongoose';
import {
  HydratedDocument,
  ObjectId,
  SchemaOptions,
  SchemaTypes,
} from 'mongoose';
import { FIELD, QUIZ_FIELD } from 'src/util/interfaces/forms.interface';

const schemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'form_fields',
  versionKey: false,
};
@Schema(schemaOptions)
export class FormFields {
  @Prop({ type: SchemaTypes.ObjectId })
  id: ObjectId;

  @Prop({ type: SchemaTypes.String, unique: true, required: true })
  field_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  name: string;

  @Prop({ type: SchemaTypes.Boolean, default: false })
  is_quiz_field: boolean;

  @Prop({ type: SchemaTypes.Mixed, required: true })
  skelton: FIELD | QUIZ_FIELD;

  @Prop({ type: SchemaTypes.Boolean, default: true })
  status: boolean;

  @Prop({ type: Date, default: null })
  deleted_at: Date;
}

export type FormFieldsDocumnet = HydratedDocument<FormFields>;

export const FormFieldsSchema = SchemaFactory.createForClass(FormFields);

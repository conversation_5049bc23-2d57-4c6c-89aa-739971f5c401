import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { AdminService } from 'src/services/admin.service';
import { OrganizationService } from 'src/services/organization.service';

@Injectable()
export class AdminOrOrganizationAuth implements CanActivate {
  constructor(
    private adminService: AdminService,
    private organizationService: OrganizationService,
  ) {}
  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    if (user.isAdmin || user.isOrganization) {
      if (user.isAdmin)
        return (await this.adminService.findOne({ admin_id: user.admin_id }))
          ? true
          : false;
      if (user.isOrganization)
        return (await this.organizationService.findOne(
          {
            organization_id: user.organization_id,
            email: user.email,
          },
          false,
          {
            ...this.organizationService.select,
            password: true,
          },
        ))
          ? true
          : false;
    }
    throw new UnauthorizedException();
  }
}

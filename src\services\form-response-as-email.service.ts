import {
  HttpStatus,
  HttpException,
  Injectable,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OrgAppConfigurationDto } from 'src/dto/org-app-configuration.dto';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Organization } from 'src/entities/organization.entity';
import { generateUUID } from 'src/util';
import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  Repository,
} from 'typeorm';
import { AppsService } from './apps.service';
import { OrganizationService } from './organization.service';
import {
  OrgAppDashboardConfigurationRepository,
  OrgAppDashboardConfigurationRepositoryDocument,
} from 'src/entities/mongodb/org-app-dashboard-configuration.enity';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { OrgAppDashboardConfigurationModule } from 'src/controllers/org-app-dashboard-configuration/org-app-dashboard-configuration.module';
import {
  CreateOrgAppDbConfigDto,
  UpdateOrgAppDbConfigDto,
} from 'src/dto/org-app-dashboard-configuration.dto';
import { OrgFormsRepositoryService } from './orgformsrepository.service';
import { faker } from '@faker-js/faker';
import {
  FormResponseAsEmailRepository,
  FormResponseAsEmailRepositoryDocument,
} from 'src/entities/mongodb/form-response-as-email-repository.entity';
import { FormResponseAsEmailDto } from 'src/dto/form-response-as-email.dto';

@Injectable()
export class FormResponseAsEmailService {
  constructor(
    @InjectModel(FormResponseAsEmailRepository.name)
    private readonly formResponseAsEmailRepository: Model<FormResponseAsEmailRepositoryDocument>,

    private readonly orgFormsRepositoryService: OrgFormsRepositoryService,
  ) {}

  /**
   * @variable select
   */
  public select: QueryOptions<FormResponseAsEmailRepository> = {
    id: false,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create FormResponseAsEmailRepository
   * @param FormResponseAsEmailDto FormResponseAsEmailDto
   * @returns Promise<FormResponseAsEmailRepository>
   */
  async create(
    formResponseAsEmailDto: FormResponseAsEmailDto,
  ): Promise<FormResponseAsEmailRepository> {
    try {
      const form = await this.orgFormsRepositoryService.findOne(
        {
          form_id: formResponseAsEmailDto.form_id,
        },
        false,
        { id: true },
      );

      if (!form) {
        throw new BadRequestException('Invalid form id');
      }

      const createdResponseEmail =
        await this.formResponseAsEmailRepository.create({
          ...formResponseAsEmailDto,
          form,
          form_res_as_email_id: generateUUID(),
        });

      await this.orgFormsRepositoryService.bulkUpdate(
        { form_id: formResponseAsEmailDto.form_id },
        { form_response_as_email: createdResponseEmail },
      );

      return createdResponseEmail;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update FormResponseAsEmailRepository
   * @param formResponseAsEmailDto FormResponseAsEmailDto
   * @returns Promise<FormResponseAsEmailRepository>
   */
  async edit(
    form_res_as_email_id: string,
    formResponseAsEmailDto: FormResponseAsEmailDto,
  ): Promise<FormResponseAsEmailRepository> {
    try {
      return await this.update(form_res_as_email_id, formResponseAsEmailDto);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  //Update the record
  async update(
    form_res_as_email_id: string,
    body: any,
  ): Promise<FormResponseAsEmailRepository> {
    try {
      return await this.formResponseAsEmailRepository.findOneAndUpdate(
        {
          form_res_as_email_id,
        },
        body,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find app dashboard configuration record by condition
   * @param condition Object "{column: value}"
   * @returns Promise<FormResponseAsEmailRepository>
   */
  async findOne(
    condition: QueryOptions<FormResponseAsEmailRepository>,
    withDeleted = false,
    select: QueryOptions<FormResponseAsEmailRepository> = this.select,
    expand = 'form',
  ): Promise<FormResponseAsEmailRepository> {
    try {
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }

      let query = this.formResponseAsEmailRepository.findOne(condition);

      if (expand) {
        query = query.populate(expand);
      }

      return await query.select(select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateOrgAppDbConfigDto {
  @IsNotEmpty()
  @IsString()
  listing_style: string;

  @IsNotEmpty()
  @IsString()
  form_id: string;

  @IsNotEmpty()
  listing_details: any;

  @IsOptional()
  avatar_field_details: any;
}

export class UpdateOrgAppDbConfigDto extends CreateOrgAppDbConfigDto {
  @IsOptional()
  @IsString()
  form_id: string;
}

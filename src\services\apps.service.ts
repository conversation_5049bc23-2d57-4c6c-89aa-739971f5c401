import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { InjectRepository } from '@nestjs/typeorm';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  Relation,
  Repository,
} from 'typeorm';

import { AppsDto } from 'src/dto/apps.dto';
import { Apps } from 'src/entities/apps.entity';
import {
  FormsRepository,
  FormsRepositoryDocument,
} from 'src/entities/mongodb/formsrepository.entity';
import {
  OrgFormsRepository,
  OrgFormsRepositoryDocument,
} from 'src/entities/mongodb/orgformsrepository.entity';
import { generateAppCode, generateUUID } from 'src/util';
import { Organization } from 'src/entities/organization.entity';
import { IndustryTypesService } from './industry-types.service';
import { IndustryAppProcessService } from './industry-app-process.service';
import { OrgAppConfigurationService } from './org-app-configuration.service';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { PaginationService } from 'src/util/pagination.service';

@Injectable()
export class AppsService {
  constructor(
    @InjectRepository(Apps, 'mysql')
    private AppsRepository: Repository<Apps>,
    @InjectRepository(Organization, 'mysql')
    private Organization: Repository<Organization>,
    @InjectModel(FormsRepository.name)
    private readonly FormRepository: Model<FormsRepositoryDocument>,
    @InjectModel(OrgFormsRepository.name)
    private readonly OrgFormRepository: Model<OrgFormsRepositoryDocument>,

    @InjectRepository(OrgAppConfiguration, 'mysql')
    private orgAppConfigurationRepository: Repository<OrgAppConfiguration>,

    private readonly industryTypesService: IndustryTypesService,

    private readonly industryAppProcessService: IndustryAppProcessService, // private readonly orgAppConfigurationService: OrgAppConfigurationService,

    private readonly paginationService: PaginationService,
  ) {}

  public select: FindOptionsSelect<Apps> = {
    id: false,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Get the all apps from database
   * @returns Promise<Apps[]>
   */
  async list(
    where: FindOptionsWhere<Apps> = null,
    select: FindOptionsSelect<Apps> = this.select,
    withDeleted: boolean = false,
    relations: FindOptionsRelations<Apps> = null,
  ): Promise<Apps[]> {
    try {
      const apps = await this.AppsRepository.find({
        where: where,
        withDeleted,
        select,
        relations,
      });

      if (where?.organizations && apps?.length) {
        await Promise.all(
          apps.map(async (app: any) => {
            app.orgAppConfiguration =
              await this.orgAppConfigurationRepository.findOne({
                where: {
                  app: { id: app.id },
                  organization: where.organizations,
                },
              });
          }),
        );
      }

      return apps;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get the all apps from database
   * @returns Promise<Apps[]>
   */
  async listWithPagination(
    where: FindOptionsWhere<Apps> = null,
    select: FindOptionsSelect<Apps> = this.select,
    withDeleted: boolean = false,
    relations: FindOptionsRelations<Apps> = null,
    page: number = 1,
    limit: number = 10,
  ): Promise<any> {
    // {
    //   apps: Apps[];
    //   total: number;
    //   page: number;
    //   limit: number;
    //   total_pages: number;
    // }
    try {
      return await this.paginationService.paginate(this.FormRepository, {});

      // Calculate the number of items to skip
      const skip = (page - 1) * limit;

      // Fetch total count of apps (for pagination info)
      const [apps, total] = await this.AppsRepository.findAndCount({
        where: where,
        withDeleted,
        select,
        relations,
        skip: skip,
        take: limit,
      });

      if (where?.organizations && apps?.length) {
        await Promise.all(
          apps.map(async (app: any) => {
            app.orgAppConfiguration =
              await this.orgAppConfigurationRepository.findOne({
                where: {
                  app: { id: app.id },
                  organization: where.organizations,
                },
              });
          }),
        );
      }

      return {
        apps,
        total: total,
        page: page,
        limit: limit,
        total_pages: Math.ceil(total / limit),
      };
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * insert operation
   * @param body AppsDto
   * @returns Promise<Boolean>
   */
  async insert(body: AppsDto): Promise<Boolean> {
    try {
      //Check if the industry type and industry app process exists
      const industry_types = await this.industryTypesService.findOne(
        { industry_type_id: body.industry_type_id },
        false,
        { id: true },
      );

      const industry_app_process = await this.industryAppProcessService.findOne(
        { industry_app_process_id: body.industry_app_process_id },
        false,
        { id: true },
      );

      if (!industry_types || !industry_app_process) {
        throw new HttpException(
          'Industry type or industry app process not found',
          HttpStatus.BAD_REQUEST,
        );
      }

      //Check Apps already exists with the same industry type and industry app process
      if (await this.findOne({ industry_types, industry_app_process })) {
        throw new HttpException(
          'App already exists with this Industry Type and Industry Business Process',
          HttpStatus.BAD_REQUEST,
        );
      }

      return (await this.AppsRepository.save({
        ...body,
        app_code: generateAppCode(body.name),
        app_id: generateUUID(),
        industry_types,
        industry_app_process,
      }))
        ? true
        : false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get One record by the where condition
   * @param where FindOptionsWhere<Apps>
   * @param withDeleted boolean
   * @param select FindOptionsSelect<Apps>
   * @returns Promise<Apps>
   */
  async findOne(
    where: FindOptionsWhere<Apps>,
    withDeleted: boolean = false,
    select: FindOptionsSelect<Apps> = this.select,
    relations?: FindOptionsRelations<Apps>,
  ): Promise<Apps> {
    try {
      let app: any = await this.AppsRepository.findOne({
        where,
        withDeleted,
        select,
        relations,
      });

      if (where.organizations && app) {
        app.orgAppConfiguration =
          await this.orgAppConfigurationRepository.findOne({
            where: {
              app: { id: app.id },
              organization: where.organizations,
            },
          });
      }

      return app;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update app by app_id
   * @param app_id string
   * @param body AppsDto
   * @returns Promise<boolean>
   */
  async update(app_id: string, body: AppsDto): Promise<boolean> {
    try {
      const data: Apps = await this.findOne({ app_id }, false, {
        ...this.select,
        id: true,
      });
      if (!data) throw new HttpException('App Not Found', HttpStatus.NOT_FOUND);
      return (await this.AppsRepository.save({ id: data.id, ...body }))
        ? true
        : false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Soft delte the app by app_id
   * @param app_id string
   * @returns Promise<boolean>
   */
  async delete(app_id: string): Promise<boolean> {
    try {
      const data = await this.AppsRepository.softDelete({ app_id });
      return data.affected ? true : false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get App by the app_id
   * @param app_id string
   * @param select
   * @returns
   */
  async getById(
    where: FindOptionsWhere<Apps>,
    select: FindOptionsSelect<Apps> = this.select,
    withDeleted: boolean = false,
  ): Promise<Apps> {
    try {
      return await this.findOne(where, withDeleted, select);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get Forms by the app_id
   * @param app_id string
   * @param select
   * @returns
   */
  async getFormsById(
    app_id: string,
    isOrganization: any = null,
    organization: any = null,
  ): Promise<any> {
    try {
      const findBy: FindOptionsWhere<Apps> = {
        app_id,
      };
      if (isOrganization) {
        const organization = await this.getOrganization(isOrganization);
        findBy.organizations = organization;
      }
      const app = await this.findOne(findBy, false, {
        ...this.select,
        id: true,
      });
      if (!app) throw new HttpException('App Not Found', HttpStatus.NOT_FOUND);
      let list;
      if (isOrganization) {
        list = await this.OrgFormRepository.find({
          $or: [
            { deleted_at: { $eq: null } },
            { deleted_at: { $exists: false } },
          ],
          app_id: app_id,
          is_sub_form: false,
          organization: isOrganization,
        }).catch((error) => {
          throw error;
        });
      } else {
        list = await this.FormRepository.find({
          $or: [
            { deleted_at: { $eq: null } },
            { deleted_at: { $exists: false } },
          ],
          is_sub_form: false,
          app_id: app_id,
          // status: true,
        }).catch((error) => {
          throw error;
        });
      }
      let existedOrgForms = [];
      if (organization) {
        const orgForms = await this.OrgFormRepository.find({
          $or: [
            { deleted_at: { $eq: null } },
            { deleted_at: { $exists: false } },
          ],
          organization: organization,
        }).select({ original_form_id: true });
        existedOrgForms = orgForms.map((form) => {
          return form.original_form_id?.toString();
        });
      }
      return list.map((form: FormsRepository) => {
        const outputObj: any = {
          name: form.name,
          app_name: app.name,
          status: form.status,
          app_id: form.app_id,
          form_id: form.form_id,
          is_sub_form: form.is_sub_form,
          has_sub_forms: form.has_sub_forms,
          icon: form?.icon,
        };
        if (organization) {
          outputObj.formExisted = existedOrgForms.includes(
            form['_id']?.toString(),
          );
        }
        return outputObj;
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Return Organization
   * @parma organization_id
   * @return Promise<Organization>
   */
  async getOrganization(organization_id): Promise<Organization> {
    return await this.Organization.findOne({
      where: { organization_id },
      select: { id: true },
    });
  }
}

import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { Injectable, Scope } from '@nestjs/common';
import { REQUEST_CONTEXT } from '../constants';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';
import { FormFieldsService } from 'src/services/formfields.service';
import { FormsRepositoryService } from 'src/services/formsrespository.service';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { ExtendedValidationArguments } from '../interfaces/validation.interface';
import { BaseValidator } from './base.validator';

@ValidatorConstraint({ name: 'FormsExists', async: true })
@Injectable()
export class FormsExistRule implements ValidatorConstraintInterface {
  constructor(private formsService: FormsRepositoryService) {}
  private value_type: any;

  async validate(value: any) {
    try {
      this.value_type = typeof value;
      if (this.value_type === 'object') {
        if (value.length) {
          const data = await this.formsService.list({
            form_id: { $in: value },
            status: true,
          });
          return data.length === value.length ? true : false;
        }
        return false;
      } else {
        const data = await this.formsService.findOne({
          form_id: value,
          status: true,
        });
        return data ? true : false;
      }
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Please select the existed ${
      this.value_type === 'object' ? 'forms' : 'form'
    } only`;
  }
}

export function FormsExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'FormsExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: FormsExistRule,
    });
  };
}
@ValidatorConstraint({ name: 'OrgFormsExists', async: true })
@Injectable()
export class OrgFormsExistRule implements ValidatorConstraintInterface {
  constructor(private formsService: OrgFormsRepositoryService) {}
  private value_type: any;

  async validate(value: any) {
    try {
      this.value_type = typeof value;
      if (this.value_type === 'object') {
        if (value.length) {
          const data = await this.formsService.find({
            form_id: { $in: value },
          });
          return data.length === value.length ? true : false;
        }
        return false;
      } else {
        const data = await this.formsService.findOne({
          form_id: value,
        });
        return data ? true : false;
      }
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Please select the existed ${
      this.value_type === 'object' ? 'forms' : 'form'
    } only`;
  }
}

export function OrgFormsExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'OrgFormsExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: OrgFormsExistRule,
    });
  };
}

@ValidatorConstraint({ name: 'HasSubFormsExists', async: true })
@Injectable()
export class HasSubFormsExistRule implements ValidatorConstraintInterface {
  constructor(
    private formsService: FormsRepositoryService,
    private orgFormsService: OrgFormsRepositoryService,
  ) {}

  async validate(value: any, args: ExtendedValidationArguments) {
    try {
      const isOrganization = args.object[REQUEST_CONTEXT].user?.isOrganization;
      if (isOrganization) {
        const data = await this.orgFormsService.findOne({
          form_id: value,
        });
        return data.has_sub_forms;
      } else {
        const data = await this.formsService.findOne({
          form_id: value,
        });
        return data.has_sub_forms;
      }
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Please select the form which has activated the Sub-Forms options.`;
  }
}

export function HasSubFormsExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'HasSubFormsExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: HasSubFormsExistRule,
    });
  };
}

@ValidatorConstraint({ name: 'FieldExists', async: true })
@Injectable()
export class FieldExistRule implements ValidatorConstraintInterface {
  constructor(private formFieldService: FormFieldsService) {}

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    try {
      const formData = args.object;
      const data = await this.formFieldService.findOne(
        {
          field_id: value,
          status: true,
        },
        false,
      );
      if (data) {
        if (formData['type']?.toLowerCase() !== 'type') {
          if (
            formData['type'] !== data?.skelton?.type ||
            formData['input_type'] !== data.skelton.input_type
          )
            return false;
        } else {
          if (formData['type'] !== data?.skelton?.type) return false;
        }
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  defaultMessage(): string {
    return `Please select the existed field only`;
  }
}

export function FieldExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'FieldExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: FieldExistRule,
    });
  };
}

@ValidatorConstraint({ name: 'FieldIdExists', async: true })
@Injectable()
export class FieldIdExistRule
  extends BaseValidator
  implements ValidatorConstraintInterface
{
  constructor(
    private formsService: FormsRepositoryService,
    private orgFormsService: OrgFormsRepositoryService,
  ) {
    super();
  }

  async validate(value: any, args: ExtendedValidationArguments) {
    try {
      const groupKey = args.object['group_key'];
      const request = this.getRequestContext();
      const formId = request?.params?.form_id;
      const key = `fields.${groupKey}.fields`;

      const find = {
        form_id: formId,
        [key]: {
          $elemMatch: { field_id: value },
        },
      };
      const isOrganization = request.user['isOrganization'];
      if (isOrganization) {
        const data = await this.orgFormsService.findOne(find);
        return data ? true : false;
      } else {
        const data = await this.formsService.findOne(find);
        return data ? true : false;
      }
    } catch (e) {
      return false;
    }
  }

  defaultMessage(): string {
    return `Please select the existed field only`;
  }
}

export function FieldIdExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'FieldIdExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: FieldIdExistRule,
    });
  };
}

@ValidatorConstraint({ name: 'CustomValidationKeysExists', async: true })
export class CustomValidationKeysExistsRule
  extends BaseValidator
  implements ValidatorConstraintInterface
{
  errorMessage = '';
  constructor(
    private formsService: FormsRepositoryService,
    private orgFormsService: OrgFormsRepositoryService,
  ) {
    super();
  }

  async validate(value: any, args: ExtendedValidationArguments) {
    try {
      const request = this.getRequestContext();
      const groupKey = request.body['group_key'];
      const fieldId = request.body['field_id'];
      const formId = request.params?.form_id;
      const key = `fields.${groupKey}.fields`;
      const find = {
        form_id: formId,
        [key]: {
          $elemMatch: { field_id: fieldId },
        },
      };
      const isOrganization = request.user['isOrganization'];

      const projection = {
        form_id: true,
        [key]: {
          field_id: true,
          type: true,
          input_type: true,
        },
      };

      const data = isOrganization
        ? await this.orgFormsService.findOne(find, false, projection)
        : await this.formsService.findOne(find, false, projection);

      const fieldsArray = data.fields?.[groupKey]?.fields || [];
      const targetField = fieldsArray.find((f) => f.field_id === fieldId);

      if (!targetField) {
        return false; // field not found
      }

      return this.mandatoryKeys(
        targetField.type,
        targetField.input_type,
        request.body,
      );
    } catch (e) {
      return false;
    }
  }

  mandatoryKeys(type: string, input_type: string, body: any) {
    const cust = body['validation_schema']['custom_validation'];

    if (type !== 'input') return true;

    switch (input_type) {
      case 'text':
        return this.filterKeys(['minLength', 'maxLength', 'inputFormat'], cust);
      case 'file':
        return this.filterKeys(['maxFiles', 'maxFileSize', 'fileFormat'], cust);
      case 'date':
        return this.filterKeys(['startDate', 'endDate'], cust);
      case 'time':
        return this.filterKeys(['startTime', 'endTime'], cust);
      case 'datetime-local':
        return this.filterKeys(['startDateTime', 'endDateTime'], cust);
      case 'number':
        return this.filterKeys(['minValue', 'maxValue'], cust);
      case 'phone':
        return this.filterKeys(['country'], cust);
      default:
        return true;
    }
  }

  filterKeys(keys: any, cust: any) {
    const missingKeys = keys.filter((key) => !(key in cust));
    missingKeys.length > 0
      ? (this.errorMessage = missingKeys + ' keys are mandatory')
      : '';
    return missingKeys.length > 0 ? false : true;
  }

  defaultMessage(): string {
    return this.errorMessage;
  }
}

export function CustomValidationKeysExists(
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'CustomValidationKeysExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: CustomValidationKeysExistsRule,
    });
  };
}

@ValidatorConstraint({ name: 'ConditionalValidationKeysExists', async: true })
export class ConditionalValidationKeysExistsRule
  extends BaseValidator
  implements ValidatorConstraintInterface
{
  errorMessage = '';
  constructor(
    private formsService: FormsRepositoryService,
    private orgFormsService: OrgFormsRepositoryService,
  ) {
    super();
  }

  async validate(value: any, args: ExtendedValidationArguments) {
    try {
      const request = this.getRequestContext();
      const groupKey =
        request.body['validation_schema']['conditional_validation'][
          'selectedGroupKey'
        ];
      const fieldId =
        request.body['validation_schema']['conditional_validation'][
          'selectedInputId'
        ];

      if (!fieldId) return true;

      const formId = request.params?.form_id;
      const key = `fields.${groupKey}.fields`;
      const find = {
        form_id: formId,
        [key]: {
          $elemMatch: { field_id: fieldId },
        },
      };
      const isOrganization = request.user['isOrganization'];

      const projection = {
        form_id: true,
        [key]: {
          field_id: true,
          type: true,
          input_type: true,
        },
      };

      const data = isOrganization
        ? await this.orgFormsService.findOne(find, false, projection)
        : await this.formsService.findOne(find, false, projection);

      const fieldsArray = data.fields?.[groupKey]?.fields || [];
      const targetField = fieldsArray.find((f) => f.field_id === fieldId);

      if (!targetField) {
        return false; // field not found
      }

      return this.mandatoryKeys(
        targetField.type,
        targetField.input_type,
        request.body,
      );
    } catch (e) {
      return false;
    }
  }

  mandatoryKeys(type: string, input_type: string, body: any) {
    const cust = body['validation_schema']['conditional_validation'];

    if (type !== 'input' && type !== 'select' && type != 'toggle') return true;
    if (type == 'select') {
      return this.filterKeys(['selectedOption'], cust);
    }
    switch (input_type) {
      case 'date':
        const compareDate = this.filterKeys(['dateComparison'], cust);
        if (compareDate) {
          if (cust['dateComparison'] == 'inbetween') {
            return this.filterKeys(
              ['selectedStartDate', 'selectedEndDate'],
              cust,
            );
          } else {
            return this.filterKeys(['selectedDate'], cust);
          }
        } else {
          return false;
        }
      case 'time':
        const compareTime = this.filterKeys(['dateComparison'], cust);
        if (compareTime) {
          if (cust['dateComparison'] == 'inbetween') {
            return this.filterKeys(
              ['selectedStartDate', 'selectedEndDate'],
              cust,
            );
          } else {
            return this.filterKeys(['selectedDate'], cust);
          }
        } else {
          return false;
        }
      case 'datetime-local':
        const compareDateTime = this.filterKeys(['dateComparison'], cust);
        if (compareDateTime) {
          if (cust['dateComparison'] == 'inbetween') {
            return this.filterKeys(
              ['selectedStartDate', 'selectedEndDate'],
              cust,
            );
          } else {
            return this.filterKeys(['selectedDate'], cust);
          }
        } else {
          return false;
        }
      case 'checkbox':
      case 'radio':
      case 'toggle':
        return this.filterKeys(['selectedOption'], cust);

      default:
        return true;
    }
  }

  filterKeys(keys: any, cust: any) {
    const missingKeys = keys.filter((key) => !(key in cust));
    missingKeys.length > 0
      ? (this.errorMessage = missingKeys + ' keys are mandatory')
      : '';
    return missingKeys.length > 0 ? false : true;
  }

  defaultMessage(): string {
    return this.errorMessage;
  }
}

export function ConditionalValidationKeysExists(
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'ConditionalValidationKeysExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: ConditionalValidationKeysExistsRule,
    });
  };
}

@ValidatorConstraint({ name: 'FormSectionExists', async: true })
@Injectable()
export class FormSectionExistRule implements ValidatorConstraintInterface {
  constructor(private formSectionService: FormSectionsRepositoryService) {}

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    try {
      const data = await this.formSectionService.findOne({
        name: { $regex: new RegExp(`^${value}$`), $options: 'i' },
      });
      return data ? false : true;
    } catch (e) {
      return false;
    }
  }

  defaultMessage(): string {
    return `Entered template was already existed`;
  }
}

export function FormSectionExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'FormSectionExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: FormSectionExistRule,
    });
  };
}

@ValidatorConstraint({ name: 'AutoFillKeysExists', async: true })
export class AutoFillKeysExistsRule
  extends BaseValidator
  implements ValidatorConstraintInterface
{
  errorMessage = '';
  constructor(
    private formsService: FormsRepositoryService,
    private orgFormsService: OrgFormsRepositoryService,
  ) {
    super();
  }

  async validate(value: any, args: ExtendedValidationArguments) {
    try {
      const request = this.getRequestContext();
      const groupKey = request.body['group_key'];
      const fieldId = request.body['field_id'];

      if (!fieldId) return true;

      const formId = request.params?.form_id;
      const key = `fields.${groupKey}.fields`;
      const find = {
        form_id: formId,
        [key]: {
          $elemMatch: { field_id: fieldId },
        },
      };
      const isOrganization = request.user['isOrganization'];

      const projection = {
        form_id: true,
        [key]: {
          field_id: true,
          type: true,
          input_type: true,
        },
      };

      const data = isOrganization
        ? await this.orgFormsService.findOne(find, false, projection)
        : await this.formsService.findOne(find, false, projection);

      const fieldsArray = data.fields?.[groupKey]?.fields || [];
      const targetField = fieldsArray.find((f) => f.field_id === fieldId);

      if (!targetField) {
        return false; // field not found
      }

      return this.mandatoryKeys(request.body);
    } catch (e) {
      return false;
    }
  }

  mandatoryKeys(body: any) {
    const cust = body['auto_fill'];
    return this.filterKeys(
      [
        'enabled',
        'source_form_id',
        'source_form_name',
        'source_section_name',
        'source_section_key',
        'source_field_name',
        'source_field_id',
      ],
      cust,
    );
  }

  filterKeys(keys: any, cust: any) {
    const missingKeys = keys.filter((key) => !(key in cust));
    missingKeys.length > 0
      ? (this.errorMessage = missingKeys + ' keys are mandatory')
      : '';
    return missingKeys.length > 0 ? false : true;
  }

  defaultMessage(): string {
    return this.errorMessage;
  }
}

export function AutoFillKeysExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'AutoFillKeysExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: AutoFillKeysExistsRule,
    });
  };
}

@ValidatorConstraint({ name: 'SourceFormIdExists', async: true })
@Injectable()
export class SourceFormIdExistsRule
  extends BaseValidator
  implements ValidatorConstraintInterface
{
  constructor(
    private formsService: FormsRepositoryService,
    private orgFormsService: OrgFormsRepositoryService,
  ) {
    super();
  }

  async validate(value: any, args: ExtendedValidationArguments) {
    try {
      const request = this.getRequestContext();
      const find = {
        form_id: value,
      };
      const isOrganization = request.user['isOrganization'];
      if (isOrganization) {
        const data = await this.orgFormsService.findOne(find);
        return data ? true : false;
      } else {
        const data = await this.formsService.findOne(find);
        return data ? true : false;
      }
    } catch (e) {
      console.log('error ', e);
      return false;
    }
  }

  defaultMessage(): string {
    return `Please select the existed Form only`;
  }
}

export function SourceFormIdExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'SourceFormIdExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: SourceFormIdExistsRule,
    });
  };
}

@ValidatorConstraint({ name: 'SourceSectionKeyExists', async: true })
@Injectable()
export class SourceSectionKeyExistsRule
  extends BaseValidator
  implements ValidatorConstraintInterface
{
  constructor(
    private formsService: FormsRepositoryService,
    private orgFormsService: OrgFormsRepositoryService,
  ) {
    super();
  }

  async validate(value: any, args: ExtendedValidationArguments) {
    try {
      const request = this.getRequestContext();
      const formId = request.body['auto_fill']['source_form_id'];

      const find = {
        form_id: formId,
        [`fields.${value}`]: { $exists: true },
      };

      const isOrganization = request.user['isOrganization'];
      if (isOrganization) {
        const data = await this.orgFormsService.findOne(find);

        return data ? true : false;
      } else {
        const data = await this.formsService.findOne(find);
        return data ? true : false;
      }
    } catch (e) {
      console.log('error ', e);
      return false;
    }
  }

  defaultMessage(): string {
    return `Please select the existed section only`;
  }
}

export function SourceSectionKeyExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'SourceSectionKeyExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: SourceSectionKeyExistsRule,
    });
  };
}

@ValidatorConstraint({ name: 'SourceFieldIdExists', async: true })
@Injectable()
export class SourceFieldIdExistsRule
  extends BaseValidator
  implements ValidatorConstraintInterface
{
  constructor(
    private formsService: FormsRepositoryService,
    private orgFormsService: OrgFormsRepositoryService,
  ) {
    super();
  }

  async validate(value: any, args: ExtendedValidationArguments) {
    try {
      const request = this.getRequestContext();
      const formId = request.body['auto_fill']['source_form_id'];
      const groupKey = request.body['auto_fill']['source_section_key'];

      const key = `fields.${groupKey}.fields`;
      const find = {
        form_id: formId,
        [key]: {
          $elemMatch: { field_id: value },
        },
      };

      const isOrganization = request.user['isOrganization'];
      if (isOrganization) {
        const data = await this.orgFormsService.findOne(find);

        return data ? true : false;
      } else {
        const data = await this.formsService.findOne(find);
        return data ? true : false;
      }
    } catch (e) {
      console.log('error ', e);
      return false;
    }
  }

  defaultMessage(): string {
    return `Please select the existed field only`;
  }
}

export function SourceFieldIdExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'SourceFieldIdExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: SourceFieldIdExistsRule,
    });
  };
}

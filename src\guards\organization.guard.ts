import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { OrganizationService } from 'src/services/organization.service';

@Injectable()
export class OrganizationAuth implements CanActivate {
  constructor(private organizationService: OrganizationService) {}
  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    if (user.isOrganization) {
      return (await this.organizationService.findOne({
        organization_id: user.organization_id,
        email: user.email,
      }))
        ? true
        : false;
    }
    throw new UnauthorizedException();
  }
}

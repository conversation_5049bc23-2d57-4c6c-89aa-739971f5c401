import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import {
  SchemaOptions,
  SchemaTypes,
  ObjectId,
  HydratedDocument,
} from 'mongoose';
import { FormsRepository } from './formsrepository.entity';

const modifiedFormFieldsSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'modified_from_fields',
  versionKey: false,
};
@Schema(modifiedFormFieldsSchemaOptions)
export class ModifiedFormFieldsRepository {
  @Prop({ type: SchemaTypes.ObjectId })
  id: ObjectId;

  @Prop({ type: SchemaTypes.String, unique: true, required: true })
  modified_form_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  organization: string;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'FormsRepository', required: true })
  original_form_id: FormsRepository;

  @Prop({ type: [{ type: SchemaTypes.String }] })
  updated_fields_id: string[];

  @Prop({ type: [{ type: SchemaTypes.String }] })
  new_fields_id: string[];

  @Prop({ type: SchemaTypes.Date })
  deleted_at: Date;
}

export type ModifiedFormFieldsRepositoryDocumnet =
  HydratedDocument<ModifiedFormFieldsRepository>;
export const ModifiedFormFieldsRepositorySchema = SchemaFactory.createForClass(
  ModifiedFormFieldsRepository,
);

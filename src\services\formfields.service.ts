import {
  HttpStatus,
  HttpException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { FormFieldsDto } from 'src/dto/formrepository.dto';
import {
  FormFields,
  FormFieldsDocumnet,
} from 'src/entities/mongodb/formfields.entity';
import { formatStringToKey, generateUUID, trimString } from 'src/util';
import { InjectModel } from '@nestjs/mongoose';
import { Model, QueryOptions } from 'mongoose';
import { FIELD } from 'src/util/interfaces/forms.interface';

@Injectable()
export class FormFieldsService {
  constructor(
    @InjectModel(FormFields.name)
    private readonly FormFieldRepository: Model<FormFieldsDocumnet>,
  ) {}

  /**
   * @variable select
   */

  private select: QueryOptions<FormFields> = {
    _id: false,
    name: true,
    skelton: true,
    field_id: true,
    created_at: true,
    updated_at: true,
    deleted_at: true,
  };

  /**
   * Create form field records
   * @param data FormFieldsDto
   * @returns Promise<FormFields>
   */

  async create(data: FormFieldsDto): Promise<FormFields> {
    try {
      const skelton: FIELD = data.skelton;
      Object.keys(skelton).forEach((key) => {
        if (key?.toLowerCase() === 'name') {
          skelton[key] = formatStringToKey(skelton[key]);
        }
      });
      skelton.name = trimString(skelton.name);
      data.skelton = skelton;
      const uuid = generateUUID();
      return await this.FormFieldRepository.create({
        ...data,
        field_id: uuid,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find form field records by condition
   * @param condition Object "{column: value}"
   * @returns Promise<FormFields>
   */

  async findOne(
    condition: QueryOptions<FormFields>,
    withDeleted = false,
  ): Promise<FormFields> {
    try {
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }
      return await this.FormFieldRepository.findOne(condition).select(
        this.select,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the form field record by id
   * @param data FormsRepoDto
   * @param field_id string
   * @returns Promise<FormFields>
   */
  async update(data: FormFieldsDto, field_id: string): Promise<FormFields> {
    try {
      const form = await this.findOne({ field_id });
      if (!form) {
        throw new NotFoundException();
      }
      return await this.FormFieldRepository.findOneAndUpdate(
        {
          field_id: form.field_id,
        },
        data,
        { returnDocument: 'after' },
      ).select(this.select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get All forms
   * @returns Promise<FormFields[]>
   */

  async list(
    condition: QueryOptions<FormFields> = { status: true },
    withDeleted = false,
    select: QueryOptions<FormFields> = this.select,
    url_flag: boolean = true,
  ): Promise<FormFields[]> {
    if (!condition) condition = { status: true };

    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    } else {
      condition['deleted_at'] = { $eq: null };
    }

    if (!select) select = this.select;

    const list = await this.FormFieldRepository.find(condition).select(
      this.select,
    );

    if (url_flag) {
      if (list.length) {
        return list;
      }
      throw new HttpException(
        'There are no fields to list',
        HttpStatus.NOT_FOUND,
      );
    }
    return list;
  }
}

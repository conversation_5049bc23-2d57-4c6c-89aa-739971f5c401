import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Put,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';

import { AdminAuth } from 'src/guards/admin.guard';
import { AdminService } from 'src/services/admin.service';

@Controller('admin')
export default class AdminController {
  constructor(private adminService: AdminService) {}

  @Get()
  @UseGuards(AuthGuard('jwt'), AdminAuth)
  async getAdminDetails(
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.adminService.findOne({
        admin_id: request.user['admin_id'],
      });
      return data
        ? response.send({
            data,
            status: true,
            message: 'Get Admin Details Successfully',
          })
        : response.status(HttpStatus.NOT_FOUND).send({
            status: false,
            message: 'Admin Not Found',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put()
  @UseGuards(AuthGuard('jwt'), AdminAuth)
  async updateOrganization(
    @Body() body: any,
    @Req() req: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.adminService.update(body, req.user);

      return data
        ? response.send({
            status: true,
            message: 'Admin Details Updated Successfully',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to update the Admin Details',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import { HttpStatus, HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Admin } from 'src/entities/admin.entity';
import { FindOptionsWhere, Repository } from 'typeorm';

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(Admin, 'mysql')
    private AdminRepository: Repository<Admin>,
  ) {}

  /**
   * Find admin records by condition
   * @param findBy Object "{column: value}"
   * @returns Promise<Admin>
   */

  async findOne(
    findBy: FindOptionsWhere<Admin>,
    withDeleted = false,
  ): Promise<Admin> {
    try {
      return await this.AdminRepository.findOne({ where: findBy, withDeleted });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the Admin Details
   * @param data
   * @returns Promise<Admin>
   */

  async update(body: any, adminDetails: any) {
    try {
      if (!body.name) {
        throw new Error('Name is required');
      }

      const admin = await this.AdminRepository.findOne({
        where: { admin_id: adminDetails.admin_id },
      });

      if (!admin) {
        throw new Error('Admin details not found');
      }

      admin.name = body.name;

      return await this.AdminRepository.save(admin);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

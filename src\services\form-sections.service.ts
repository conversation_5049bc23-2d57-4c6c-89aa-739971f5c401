import {
  Body,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  FormSectionRepoDto,
  UpdateFormSectionRepoDto,
} from 'src/dto/formrepository.dto';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  FormSectionsRepository,
  FormSectionsRepositoryDocument,
} from 'src/entities/mongodb/form-sections.entity';
import { generateUUID } from 'src/util';
import { FormFields } from 'src/entities/mongodb/formfields.entity';

@Injectable()
export class FormSectionsRepositoryService {
  constructor(
    @InjectModel(FormSectionsRepository.name)
    private readonly FormSectionsRepository: Model<FormSectionsRepositoryDocument>,
  ) {}

  /**
   * @variable select
   */

  private select: QueryOptions<FormFields> = {
    _id: false,
    name: true,
    description: true,
    section_id: true,
    fields: true,
    created_at: true,
    updated_at: true,
    deleted_at: true,
  };

  /**
   * Create Form Section
   * @param data FormSectionRepoDto
   * @returns Promise<FormSectionsRepository>
   */
  async create(data: FormSectionRepoDto): Promise<FormSectionsRepository> {
    try {
      data.fields = data.fields.map((field) => {
        return {
          ...field,
          field_id: generateUUID(),
        };
      });

      return this.FormSectionsRepository.create({
        ...data,
        section_id: generateUUID(),
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update the form section record by id
   * @param data UpdateFormSectionRepoDto
   * @param section_id string
   * @returns Promise<FormSectionsRepository>
   */
  async update(
    data: UpdateFormSectionRepoDto,
    section_id: string,
  ): Promise<FormSectionsRepository> {
    try {
      const form = await this.findOne({ section_id });

      if (!form) {
        throw new NotFoundException('Section Not Found');
      }

      const existName = await this.findOne({
        name: data.name,
        section_id: { $ne: section_id },
      });
      if (existName)
        throw new HttpException(
          'Entered template was already existed',
          HttpStatus.CONFLICT,
        );

      data.fields = data.fields.map((field) => {
        if (!field.field_id) {
          field.field_id = generateUUID();
        }
        return field;
      });

      return await this.FormSectionsRepository.findOneAndUpdate(
        {
          section_id: form.section_id,
        },
        data,
        { returnDocument: 'after' },
      ).select(this.select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find form section records by condition
   * @param condition Object "{column: value}"
   * @returns Promise<FormSectionsRepository>
   */
  async findOne(
    condition: QueryOptions<FormSectionsRepository>,
    withDeleted = false,
  ): Promise<FormSectionsRepository> {
    try {
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }
      return await this.FormSectionsRepository.findOne(condition).select(
        this.select,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get All Form Sections
   * @returns Promise<FormSectionsRepository[]>
   */

  async findAll(
    condition: QueryOptions<FormSectionsRepository> = { status: true },
    withDeleted = false,
    select: QueryOptions<FormSectionsRepository> = this.select,
    url_flag: boolean = false,
  ): Promise<FormSectionsRepository[]> {
    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    } else {
      condition['deleted_at'] = { $eq: null };
    }

    if (!select) select = this.select;

    const list = await this.FormSectionsRepository.find(condition).select(
      this.select,
    );

    if (url_flag) {
      if (list.length) {
        return list;
      }
      throw new HttpException(
        'There are no fields to list',
        HttpStatus.NOT_FOUND,
      );
    }
    return list;
  }

  /**
   * Delete form section records by condition
   * @returns Promise<FormSectionsRepository>
   */
  async delete(section_id: string): Promise<FormSectionsRepository> {
    try {
      const formSection = await this.findOne({ section_id });

      if (!formSection) {
        throw new NotFoundException();
      }

      return await this.FormSectionsRepository.findOneAndUpdate(
        { section_id },
        {
          deleted_at: Date.now(),
        },
        {
          new: true,
        },
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Check section name was existed in user record or not
   * @param name string
   * @returns Promise<FormSectionsRepository>
   */
  async checkExistedSectionNameOrNot(
    name: string,
    id: string,
  ): Promise<FormSectionsRepository> {
    const find: QueryOptions<FormSectionsRepository> = {
      name: { $regex: new RegExp(`^${name}$`), $options: 'i' },
      section_id: { $ne: id },
    };
    return await this.findOne(find, false);
  }
}

import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { generateUUID } from 'src/util';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositoryDocument,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import { OnBoardOrgChecklistRepoDto } from 'src/dto/onboard-org-checklist.dto';

@Injectable()
export class OnBoardOrgChecklistService {
  constructor(
    @InjectModel(OnBoardOrgChecklistRepository.name)
    private readonly OnBoardOrgChecklistRepository: Model<OnBoardOrgChecklistRepositoryDocument>,
  ) {}

  /**
   * @variable select
   */

  private select: QueryOptions<OnBoardOrgChecklistRepository> = {
    _id: false,
    status: false,
    createdAt: false,
    updatedAt: false,
    deleted_at: false,
  };

  /**
   * Create OnBoardOrgChecklist
   * @param OnBoardOrgChecklistRepoDto OnBoardOrgChecklistRepoDto
   * @returns Promise<createOnBoardOrgChecklistRepoDto>
   */
  async create(
    OnBoardOrgChecklistRepoDto: OnBoardOrgChecklistRepoDto,
    organization_id: string,
  ): Promise<OnBoardOrgChecklistRepository> {
    try {
      const checklistDetails = await this.findOne({
        organization_id,
      });

      if (checklistDetails) {
        OnBoardOrgChecklistRepoDto.checklist =
          OnBoardOrgChecklistRepoDto.checklist.map((item) => {
            if (!item?.item_id) {
              item.item_id = generateUUID();
            }
            return item;
          });
        return await this.update(
          checklistDetails.org_checklist_id,
          OnBoardOrgChecklistRepoDto,
        );
      }

      OnBoardOrgChecklistRepoDto.checklist =
        OnBoardOrgChecklistRepoDto.checklist.map((item) => {
          if (!item?.item_id) {
            item.item_id = generateUUID();
          }
          return item;
        });

      return await this.OnBoardOrgChecklistRepository.create({
        ...OnBoardOrgChecklistRepoDto,
        organization_id,
        org_checklist_id: generateUUID(),
      });
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update OnBoardOrgChecklist
   * @param updateOnBoardOrgChecklistRepoDto UpdateOnBoardOrgChecklistRepoDto.
   * @param checklist_id string
   * @returns Promise<OnBoardOrgChecklistRepository>
   */
  async update(
    org_checklist_id: string,
    updateOnBoardOrgChecklistRepoDto: OnBoardOrgChecklistRepoDto,
  ): Promise<OnBoardOrgChecklistRepository> {
    try {
      return await this.OnBoardOrgChecklistRepository.findOneAndUpdate(
        {
          org_checklist_id,
        },
        updateOnBoardOrgChecklistRepoDto,
        { returnDocument: 'after' },
      ).select(this.select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find OnBoardOrgChecklist by condition
   * @param condition Object "{column: value}"
   * @returns Promise<OnBoardOrgChecklistRepository>
   */
  async findOne(
    condition: QueryOptions<OnBoardOrgChecklistRepository>,
    withDeleted = false,
    select: QueryOptions<OnBoardOrgChecklistRepository> = this.select,
  ): Promise<OnBoardOrgChecklistRepository> {
    try {
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }

      return await this.OnBoardOrgChecklistRepository.findOne(condition).select(
        select,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get All OnBoardOrgChecklist
   * @returns Promise<OnBoardOrgChecklistRepository[]>
   */
  async findAll(
    condition: QueryOptions<OnBoardOrgChecklistRepository> = { status: true },
    withDeleted = false,
    select: QueryOptions<OnBoardOrgChecklistRepository> = this.select,
    url_flag: boolean = true,
  ): Promise<OnBoardOrgChecklistRepository[]> {
    if (!condition) condition = { status: true };

    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    } else {
      condition['deleted_at'] = { $eq: null };
    }
    if (!select) select = this.select;

    const list = await this.OnBoardOrgChecklistRepository.find(
      condition,
    ).select(this.select);

    if (url_flag) {
      if (list.length) {
        return list;
      }
      throw new HttpException(
        'There are no clients to list',
        HttpStatus.NOT_FOUND,
      );
    }
    return list;
  }
}

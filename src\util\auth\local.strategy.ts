import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import AuthService from 'src/services/auth.service';
import { User } from 'src/entities/user.entity';
import { AppsService } from 'src/services/apps.service';
import EncryptionService from 'src/services/encryption.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(
    private appService: AppsService,
    private authService: AuthService,
    private encryptionService: EncryptionService,
  ) {
    super({
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    username: string,
    password: string,
  ): Promise<User> {
    const app = await this.appService.findOne(
      {
        app_code: req.headers['kleza-app-code'],
      },
      false,
      { id: true, app_id: true },
      {
        industry_app_process: true,
      },
    );

    if (
      !app ||
      (!req.headers['organization-id'] && !req.headers['organization-passkey'])
    ) {
      throw new UnauthorizedException('Missing Organization ID or Passkey');
    }

    let organization_id = req.headers['organization-id'];

    if (req.headers['organization-passkey']) {
      try {
        organization_id = this.encryptionService.decryption(
          req.headers['organization-passkey'].toString(),
        );
      } catch (e) {
        throw new UnauthorizedException('You are entering wrong passkey');
      }
    }

    let user: any;

    const onboardingApps = ['HC_OBRD'];

    const caregiverApps = ['HC_CARE'];

    if (onboardingApps.includes(app?.industry_app_process?.process_code)) {
      user = await this.authService.validateOnboardingEmployeeOnBoarding(
        username,
        password,
        organization_id,
        app.app_id,
      );
    }else if(caregiverApps.includes(app?.industry_app_process?.process_code)){
      user = await this.authService.validateCaregiver(
        username,
        password,
        organization_id,
        app.app_id,
      );
    }else {
      user = await this.authService.validateUser(
        username,
        password,
        organization_id,
      );

      if (
        user &&
        user?.apps.filter(
          (app) => app.app_code == req.headers['kleza-app-code'],
        ).length == 0
      ) {
        throw new UnauthorizedException(
          'You are entering invalid username or password',
        );
      }
    }

    if (!user) {
      throw new UnauthorizedException(
        'You are entering invalid username or password',
      );
    }

    return {...user, app_id: app.app_id};
  }
}

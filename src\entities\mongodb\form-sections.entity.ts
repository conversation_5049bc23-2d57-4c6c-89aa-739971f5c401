import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  HydratedDocument,
  ObjectId,
  SchemaOptions,
  SchemaTypes,
} from 'mongoose';
import { FIELD, GROUP } from 'src/util/interfaces/forms.interface';

const FormSectionsSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'form_sections',
  versionKey: false,
};

@Schema(FormSectionsSchemaOptions)
export class FormSectionsRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ unique: true, type: SchemaTypes.String, required: true })
  section_id: string;

  @Prop({ type: SchemaTypes.String, required: true, trim: true, unique: true })
  name: string;

  @Prop({ type: SchemaTypes.String, default: null, trim: true })
  description: string;

  @Prop({ type: SchemaTypes.Mixed })
  fields: FIELD[];

  @Prop({ default: true, type: SchemaTypes.Boolean })
  status: boolean;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type FormSectionsRepositoryDocument =
  HydratedDocument<FormSectionsRepository>;
export const FormSectionsRepositorySchema = SchemaFactory.createForClass(
  FormSectionsRepository,
);

import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { Injectable } from '@nestjs/common';

import { RequestContextService } from '../interceptor/request.interceptor';

@Injectable()
@ValidatorConstraint({ name: 'BaseValidator', async: true })
export abstract class BaseValidator implements ValidatorConstraintInterface {
  constructor() {}
  abstract validate(
    value: any,
    validationArguments?: ValidationArguments,
  ): Promise<boolean>;
  defaultMessage(args?: ValidationArguments): string {
    return 'Validation failed';
  }

  protected getRequestContext() {
    return RequestContextService.getRequest();
  }
}

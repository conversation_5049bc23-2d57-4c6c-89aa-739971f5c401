import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Res,
  Query,
  NotFoundException,
  Post,
  UsePipes,
  ValidationPipe,
  UseGuards,
  Body,
  Req,
  Param,
  Patch,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response, Request } from 'express';
import { OrgAppConfigurationDto } from 'src/dto/org-app-configuration.dto';
import { AdminOrOrganizationAuth } from 'src/guards/adminOrOrganization.guard';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';

import { IndustryTypesService } from 'src/services/industry-types.service';
import { OrgAppConfigurationService } from 'src/services/org-app-configuration.service';

@Controller('org-app-configuration')
export default class OrgAppConfigurationController {
  constructor(
    private readonly orgAppConfigurationService: OrgAppConfigurationService,
  ) {} // private industryAppProcessService: IndustryAppProcessService, // private industryTypesService: IndustryTypesService,

  /**
   * Create OrgAppConfiguration
   * @param orgAppConfigurationDto OrgAppConfigurationDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('/:app_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async create(
    @Body() orgAppConfigurationDto: OrgAppConfigurationDto,
    @Param('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: OrgAppConfigurationDto =
        await this.orgAppConfigurationService.create(
          orgAppConfigurationDto,
          app_id,
          request.user['organization_id'],
        );

      return data
        ? response.status(HttpStatus.CREATED).send({
            status: true,
            message: 'Application details updated successfully .',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to create the App.',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update OrgAppConfiguration
   * @param orgAppConfigurationDto OrgAppConfigurationDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('/:org_app_configuration_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async update(
    @Body() orgAppConfigurationDto: OrgAppConfigurationDto,
    @Param('org_app_configuration_id') org_app_configuration_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: OrgAppConfigurationDto =
        await this.orgAppConfigurationService.update(
          org_app_configuration_id,
          orgAppConfigurationDto,
        );

      return data
        ? response.status(HttpStatus.OK).send({
            status: true,
            message: 'Application details updated successfully .',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to create the App.',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // @Get('/')
  // async find(
  //   @Res() response: Response,
  //   @Query('industry_type_id') industry_type_id: string,
  // ): Promise<Response> {
  //   try {
  //     let find: any = {};

  //     if (industry_type_id) {
  //       const industryType = await this.industryTypesService.findOne(
  //         {
  //           industry_type_id,
  //         },
  //         false,
  //         { id: true },
  //       );

  //       if (!industryType) {
  //         throw new NotFoundException('Industry type not found');
  //       }

  //       find.industry_type = industryType;
  //     }

  //     const data = await this.industryAppProcessService.find(find);

  //     return response.send({
  //       status: true,
  //       message: 'Industry Types Process fetched successfully',
  //       data,
  //     });
  //   } catch (error) {
  //     throw new HttpException(
  //       error?.message || 'Something went wrong. Please try again later.',
  //       error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }
}

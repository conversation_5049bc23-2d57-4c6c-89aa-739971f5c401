import {
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
} from '@nestjs/common';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { generateUUID } from 'src/util';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositoryDocument,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import { OnBoardEmpChecklistRepoDto } from 'src/dto/onboard-emp-checklist.dto';
import { OnboardingEmployeeService } from './onboarding-employee.service';
import { OnBoardOrgChecklistService } from './onboard-org-checklist.service';
import { OnBoardOrgChecklistRepository } from 'src/entities/mongodb/onboard-org-checklist-repository.entity';

@Injectable()
export class OnBoardEmpChecklistService {
  constructor(
    @InjectModel(OnBoardEmpChecklistRepository.name)
    private readonly OnBoardEmpChecklistRepository: Model<OnBoardEmpChecklistRepositoryDocument>,

    // private readonly OnboardingEmployeeService: OnboardingEmployeeService,
    @Inject(forwardRef(() => OnboardingEmployeeService))
    private readonly OnboardingEmployeeService: OnboardingEmployeeService,

    private readonly OnBoardOrgChecklistService: OnBoardOrgChecklistService,
  ) {}

  /**
   * @variable select
   */

  private select: QueryOptions<OnBoardEmpChecklistRepository> = {
    _id: false,
    status: false,
    createdAt: false,
    updatedAt: false,
    deleted_at: false,
  };

  /**
   * Create OnBoardEmpChecklist
   * @param OnBoardEmpChecklistRepoDto OnBoardEmpChecklistRepoDto
   * @returns Promise<OnBoardEmpChecklistRepoDto>
   */
  async create(
    OnBoardEmpChecklistRepoDto: OnBoardEmpChecklistRepoDto,
    onboarding_employee_id: string,
    organization_id: string,
  ): Promise<OnBoardEmpChecklistRepository> {
    try {
      const checklistDetails: OnBoardOrgChecklistRepository =
        await this.OnBoardOrgChecklistService.findOne(
          {
            organization_id,
          },
          false,
          { id: true },
        );

      const onboarding_employee = await this.OnboardingEmployeeService.findOne(
        { onboarding_employee_id },
        false,
        { id: true },
      );

      if (!onboarding_employee) {
        throw new HttpException(
          'Onboarding Employee not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const OnBoardEmpChecklist = await this.findOne({
        onboarding_employee_id,
      });

      if (OnBoardEmpChecklist) {
        return await this.update(
          OnBoardEmpChecklist.emp_checklist_id,
          OnBoardEmpChecklistRepoDto,
        );
      }

      const CreateOnBoardEmpChecklist =
        await this.OnBoardEmpChecklistRepository.create({
          ...OnBoardEmpChecklistRepoDto,
          onboarding_employee_id,
          emp_checklist_id: generateUUID(),
        });

      CreateOnBoardEmpChecklist.onboard_org_checklist = checklistDetails;

      return await CreateOnBoardEmpChecklist.save();
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update OnBoardEmpChecklist
   * @param updateOnBoardEmpChecklistRepoDto UpdateOnBoardEmpChecklistRepoDto.
   * @param checklist_id string
   * @returns Promise<OnBoardEmpChecklistRepository>
   */
  async update(
    emp_checklist_id: string,
    updateOnBoardEmpChecklistRepoDto: OnBoardEmpChecklistRepoDto,
  ): Promise<OnBoardEmpChecklistRepository> {
    try {
      return await this.OnBoardEmpChecklistRepository.findOneAndUpdate(
        {
          emp_checklist_id,
        },
        updateOnBoardEmpChecklistRepoDto,
        { returnDocument: 'after' },
      ).select(this.select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find OnBoardEmpChecklist by condition
   * @param condition Object "{column: value}"
   * @returns Promise<OnBoardEmpChecklistRepository>
   */
  async findOne(
    condition: QueryOptions<OnBoardEmpChecklistRepository>,
    withDeleted = false,
    select: QueryOptions<OnBoardEmpChecklistRepository> = this.select,
  ): Promise<OnBoardEmpChecklistRepository> {
    try {
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }

      return await this.OnBoardEmpChecklistRepository.findOne(condition)
        .select(select)
        .populate('onboard_org_checklist');
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

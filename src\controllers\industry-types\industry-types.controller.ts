import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Res,
} from '@nestjs/common';
import { Response } from 'express';

import { IndustryTypesService } from 'src/services/industry-types.service';

@Controller('industry-types')
export default class IndustryTypesController {
  constructor(private industryTypesService: IndustryTypesService) {}

  @Get('/')
  async find(@Res() response: Response): Promise<Response> {
    try {
      const data = await this.industryTypesService.find({});

      return response.send({
        status: true,
        message: 'Industry Types fetched successfully',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  OneToMany,
  ManyToOne,
  JoinTable,
  PrimaryColumn,
  ManyToMany,
} from 'typeorm';

import { Apps } from './apps.entity';
import { Organization } from './organization.entity';
import { THEME } from 'src/util/interfaces/theme.interface';

@Entity()
export class Themes {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ unique: true, type: 'varchar' })
  theme_id: string;

  @Column({ type: 'json' })
  theme: THEME;

  @Column({ type: Boolean, default: true })
  status: boolean;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @ManyToMany(() => Apps, (app: Apps) => app.themes, {
    eager: false,
    nullable: false,
  })
  @JoinTable({
    name: 'app_themes',
    joinColumn: {
      name: 'theme_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'app_id',
      referencedColumnName: 'id',
    },
  })
  apps: Apps[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

@Entity('app_themes')
export class AppThemes {
  @PrimaryColumn({ type: 'int' })
  app_id: number;

  @PrimaryColumn({ type: 'int' })
  theme_id: number;

  @OneToOne(() => Themes)
  @JoinTable()
  theme: Themes;

  @OneToOne(() => Apps)
  @JoinTable()
  app: Apps;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

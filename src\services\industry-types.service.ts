import { HttpStatus, HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { FindOptionsSelect, FindOptionsWhere, Repository } from 'typeorm';

@Injectable()
export class IndustryTypesService {
  constructor(
    @InjectRepository(IndustryTypes, 'mysql')
    private industryTypesRepository: Repository<IndustryTypes>,
  ) {}

  public select: FindOptionsSelect<IndustryTypes> = {
    id: false,
    industry_type_id: true,
    industry_type: true,
    description: true,
    status: true,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  async findOne(
    findBy: FindOptionsWhere<IndustryTypes>,
    withDeleted = false,
    select = this.select,
  ): Promise<IndustryTypes> {
    try {
      return await this.industryTypesRepository.findOne({
        where: findBy,
        withDeleted,
        select,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find Industry Types
   * @param findBy Object "{column: value}"
   * @returns Promise<IndustryTypes[]>
   */

  async find(
    findBy: FindOptionsWhere<IndustryTypes>,
    withDeleted = false,
    select = this.select,
  ): Promise<IndustryTypes[]> {
    try {
      return await this.industryTypesRepository.find({
        where: findBy,
        withDeleted,
        select,
        relations: {
          apps: true,
          industry_app_process: true,
          organizations: true,
        },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  HydratedDocument,
  ObjectId,
  SchemaOptions,
  SchemaTypes,
} from 'mongoose';

const ESignRequestLogsSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'e_sign_request_logs',
  versionKey: false,
};

@Schema(ESignRequestLogsSchemaOptions)
export class ESignRequestLogsRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ unique: true, type: SchemaTypes.String, required: true })
  e_sign_request_logs_id: string;

  @Prop({ type: SchemaTypes.String })
  user_id: string;

  @Prop({ type: SchemaTypes.String })
  app_id: string;

  @Prop({ type: SchemaTypes.String })
  organization_id: string;

  @Prop({ type: SchemaTypes.Number })
  expiry_timestamp: number;

  @Prop({ type: SchemaTypes.String })
  client_id: string;

  @Prop({ type: SchemaTypes.Mixed })
  forms: any;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type ESignRequestLogsRepositoryDocument =
  HydratedDocument<ESignRequestLogsRepository>;
export const ESignRequestLogsSchema = SchemaFactory.createForClass(
  ESignRequestLogsRepository,
);

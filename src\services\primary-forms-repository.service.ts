import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  ClientsRepository,
  ClientsRepositoryDocument,
} from 'src/entities/mongodb/clientsrepository.entity';
import { AppsService } from './apps.service';
import {
  PrimaryFormsRepository,
  PrimaryFormsRepositoryDocument,
} from 'src/entities/mongodb/primary-forms-repository.entity';
import EncryptionService from './encryption.service';

@Injectable()
export class PrimaryFormsRepositoryService {
  constructor(
    @InjectModel(PrimaryFormsRepository.name)
    private readonly primaryFormsRepository: Model<PrimaryFormsRepositoryDocument>,

    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * @variable select
   */

  private select: QueryOptions<PrimaryFormsRepository> = {
    _id: false,
    primary_form_id: true,
    original_form: true,
    values: true,
    organization_id: true,
    app_id: true,
    created_at: true,
    updated_at: true,
    deleted_at: true,
  };

  /**
   * Get list of primary forms
   * @returns Promise<ClientsRepository[]>
   */
  async find(
    condition: QueryOptions<PrimaryFormsRepository> = { status: true },
    withDeleted = false,
    select: QueryOptions<PrimaryFormsRepository> = this.select,
    expand: string[] = ['original_form', 'values'],
  ): Promise<PrimaryFormsRepository[]> {
    if (!condition) condition = { status: true };

    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    } else {
      condition['deleted_at'] = { $eq: null };
    }

    if (!select) select = this.select;

    let query = this.primaryFormsRepository.find(condition);

    if (expand) {
      query = query.populate(expand);
    }

    let primary_forms: PrimaryFormsRepository[] = await query.select(
      this.select,
    );

    primary_forms = primary_forms.map(
      (primary_form: PrimaryFormsRepository) => {
        if (
          primary_form.values &&
          primary_form.values.values_encryption_status
        ) {
          primary_form.values.values = this.encryptionService.decryption(
            primary_form.values.values,
          );
          return primary_form;
        }
      },
    );

    return primary_forms;
  }
}

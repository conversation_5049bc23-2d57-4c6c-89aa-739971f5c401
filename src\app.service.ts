import { Injectable } from '@nestjs/common';
import { writeFileSync, readFileSync } from 'fs';

@Injectable()
export class AppService {
  getHello(): string {
    return 'Hello World!';
  }

  async storeJsonData(data: any) {
    try {
      await writeFileSync('./demoFlow.json', JSON.stringify(data));
    } catch (error) {
      throw error;
    }
  }

  async readJsonData(onboarding_employee_id?: string) {
    try {
      let response: any = await readFileSync('./demoFlow.json', {
        encoding: 'utf8',
        flag: 'r',
      });

      if (onboarding_employee_id) {
        response =
          (response &&
            response.length &&
            JSON.parse(response).find(
              (item: any) =>
                item.onboarding_employee_id === onboarding_employee_id,
            )) ||
          null;
      }

      return response;
    } catch (error) {
      throw error;
    }
  }
}

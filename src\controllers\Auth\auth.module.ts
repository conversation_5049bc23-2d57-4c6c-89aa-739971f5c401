import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { readFileSync } from 'fs';
import { join } from 'path';
import AuthService from 'src/services/auth.service';
import { LocalStrategy } from 'src/util/auth/local.strategy';
import AuthController from './auth.controller';
import { OrganizationModule } from '../organization/organization.module';
import { UserModule } from '../user/user.module';
import { JwtStrategy } from 'src/util/auth/jwt.strategy';
import { AdminModule } from '../admin/admin.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Admin } from 'src/entities/admin.entity';
import { Organization } from 'src/entities/organization.entity';
import { User } from 'src/entities/user.entity';
import { AppsModule } from '../apps/apps.module';
import { OnboardingEmployeeModule } from '../employee/onboarding-employee.module';
import { OrgAppConfigurationService } from 'src/services/org-app-configuration.service';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import EncryptionService from 'src/services/encryption.service';
import { CaregiversService } from 'src/services/caregivers.service';
import { Caregivers } from 'src/entities/caregivers.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [Admin, Organization, User, OrgAppConfiguration,Caregivers],
      'mysql',
    ),
    AppsModule,
    UserModule,
    AdminModule,
    PassportModule,
    OnboardingEmployeeModule,
    JwtModule.registerAsync({
      //TODO  Setup before application running first time
      /**
       * First create directory with name "keys" and run the below commands
       * ssh-keygen -t rsa -b 4096 -m PEM -f keys/klezaforms.key
       * openssl rsa -in keys/klezaforms.key -pubout -outform PEM -out keys/klezaforms.key.pub
       */
      useFactory: () => ({
        privateKey: readFileSync(
          join(process.cwd(), 'keys/klezaforms.key'),
          'utf-8',
        ),
        signOptions: {
          algorithm: 'RS512',
        },
      }),
    }),
    OrganizationModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    LocalStrategy,
    JwtStrategy,
    OrgAppConfigurationService,
    EncryptionService,
    CaregiversService
  ],
})
export class AuthModule {}

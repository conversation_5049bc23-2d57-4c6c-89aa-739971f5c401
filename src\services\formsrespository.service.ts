import {
  HttpStatus,
  HttpException,
  Injectable,
  NotFoundException,
  NotAcceptableException,
} from '@nestjs/common';
import { ObjectID } from 'mongodb';
import {
  FormsRepository,
  FormsRepositoryDocument,
} from 'src/entities/mongodb/formsrepository.entity';
import {
  AutoFieldDto,
  AutoCreateFormsRepoDto,
  FormDetails,
  FormsRepoDto,
  UpdateFormsRepoDto,
  UpdateField,
  DeleteFieldDto,
  UpdateIndex,
  CopyFieldDto,
  AddSectionDto,
  RemoveSectionDto,
  UpdateGroupIndex,
  UpdateFormsIndex,
  FormIndex,
  CustomValidationBody,
} from 'src/dto/formrepository.dto';
import { Response } from 'express';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  FormValuesRepository,
  FormValuesRepositoryDocument,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import { formatStringToKey, generateUUID, trimString } from 'src/util';
import {
  APP_FORMS,
  AUTO_CREATE_FORM,
  FIELD,
  GROUP,
} from 'src/util/interfaces/forms.interface';
import { AppsService } from './apps.service';
import { FormFieldsService } from './formfields.service';
import { Apps } from 'src/entities/apps.entity';
import { GlobalService } from './global.service';
import { FormSectionsRepositoryService } from './form-sections.service';
import EncryptionService from './encryption.service';

@Injectable()
export class FormsRepositoryService {
  constructor(
    @InjectModel(FormsRepository.name)
    private readonly FormRepository: Model<FormsRepositoryDocument>,

    @InjectModel(FormValuesRepository.name)
    private readonly FormValueRepository: Model<FormValuesRepositoryDocument>,

    private readonly appsService: AppsService,

    private readonly fieldService: FormFieldsService,

    private readonly globalService: GlobalService,

    private readonly FormSectionsRepositoryService: FormSectionsRepositoryService,

    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * @variable select
   */
  public select: QueryOptions<FormsRepository> = {
    id: true,
    _id: false,
    name: true,
    icon: true,
    description: true,
    fields: true,
    status: true,
    is_disable_form_response: true,
    has_multiple_form_responses: true,
    form_id: true,
    app_id: true,
    has_sub_forms: true,
    is_quiz_form: true,
    is_sub_form: true,
    order_position: true,
    validationschema: true,
    created_at: true,
    updated_at: true,
  };

  /**
   * Get the forms based on App
   * @param app_id string
   * @returns Promise<APP_FORMS[]>
   */
  async getAllFormsByApps(app_id?: string): Promise<APP_FORMS[] | APP_FORMS> {
    try {
      if (app_id) {
        const app: Apps = await this.appsService.findOne({ app_id });
        if (!app) {
          throw new NotFoundException('App Not Found');
        }
        const forms = await this.list(
          { app_id: app.app_id, is_sub_form: false },
          false,
          {
            form_id: true,
            name: true,
            status: true,
            _id: false,
            has_sub_forms: true,
            order_position: true,
            icon: true,
          },
          false,
        );
        const data: APP_FORMS = {
          name: app.name,
          app_id: app.app_id,
          status: app.status,
          description: app.description,

          forms,
        };
        if (data) return data;
        throw new HttpException('Please add Apps first', HttpStatus.NOT_FOUND);
      } else {
        const apps: Apps[] = await this.appsService.list();
        const data: APP_FORMS[] = await Promise.all(
          apps.map(async (app: Apps): Promise<APP_FORMS> => {
            const forms = await this.list(
              { app_id: app.app_id, is_sub_form: false },
              false,
              {
                form_id: true,
                name: true,
                status: true,
                _id: false,
                has_sub_forms: true,
                order_position: true,
              },
              false,
            );
            return {
              name: app.name,
              app_id: app.app_id,
              status: app.status,
              description: app.description,
              forms,
            };
          }),
        ).catch((error) => {
          throw error;
        });
        if (data.length) return data;
        throw new HttpException('Please add Apps first', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create Form
   * @param data FormsRepoDto
   * @returns Promise<FormsRepository>
   */
  async create(data: FormsRepoDto): Promise<FormsRepository> {
    try {
      const groups: GROUP[] = data.groups;
      let validationschema: any = {};
      let fields: Record<string, GROUP> = {};
      groups
        .map((group: GROUP) => {
          const group_title = formatStringToKey(group.group_title);
          group.fields.forEach((field: FIELD) => {
            field.field_id = generateUUID();
            field.name = formatStringToKey(field.name);
            if (group_title in validationschema) {
              validationschema[group_title][field.name] = {
                type: field?.input_type || 'text',
                ...field.validation_schema,
              };
            } else {
              validationschema = {
                ...validationschema,
                [group_title]: {
                  [field.name]: {
                    type: field?.input_type || 'text',
                    ...field.validation_schema,
                  },
                },
              };
            }
          });
          return group;
        })
        .forEach((group: GROUP) => {
          const group_title = formatStringToKey(group.group_title);
          if (group_title in fields) {
            fields[group_title] = group;
          } else {
            fields = {
              ...fields,
              [group_title]: group,
            };
          }
        });
      const savedData = {
        fields,
        status: true,
        name: data.name,
        description: data.description,
        validationschema,
        app_id: data.app_id,
        order_position: data?.order_position ? data?.order_position : 0,
        form_id: generateUUID(),
      };
      return await this.FormRepository.create(savedData).catch((error) => {
        throw error;
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Auto Create the form with untitled form under the requested app or default app
   * @param body CreateFormsRepoDto
   * @returns Promise<FormsRepository>
   */
  async autoCreate(body: AutoCreateFormsRepoDto): Promise<FormsRepository> {
    try {
      const data: AUTO_CREATE_FORM =
        await this.globalService.generateAutoCreateFormData(body);

      const form = await this.FormRepository.create(data).catch((error) => {
        throw error;
      });

      if (form) {
        if (data.is_sub_form) {
          const data = await this.FormRepository.findOneAndUpdate(
            { form_id: body.main_form_id },
            {
              $addToSet: {
                sub_forms: form,
              },
            },
          );
          await this.FormRepository.findOneAndUpdate(
            { form_id: form.form_id },
            { main_form_id: data._id },
          );
        }
        return form;
      }
      throw {
        status: HttpStatus.CONFLICT,
        message: 'Unable to create the Form',
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add field to form by form_id
   * @param form_id string
   * @param body AutoFieldDto
   * @returns Promise<FIELD>
   */
  async addField(form_id: string, body: AutoFieldDto): Promise<FIELD> {
    try {
      const form = await this.findOne({ form_id }, false, {
        form_id: true,
        fields: true,
        status: true,
        has_sub_forms: true,
        validationschema: true,
      });

      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };

      if (form.has_sub_forms)
        throw {
          status: HttpStatus.BAD_REQUEST,
          message:
            'Unable to add field due requested for has sub forms. Please try to add fields in sub forms',
        };
      const field = body.field;
      const group_title = body.group_title || form.name;
      const group_key = body.group_key || formatStringToKey(group_title);
      const group_description = body.group_description || '';
      const form_fields = form.fields;

      const form_field: FIELD = {
        ...field,
        name: `${field.name}_${Date.now()}`,
        field_id: generateUUID(),
        field_index:
          field?.field_index ||
          (form_fields && form_fields[group_key]?.fields?.length) ||
          0,
      };

      const validationschema = this.globalService.generateValidationSchema(
        form,
        group_key,
        form_field,
      );

      if (Object.keys(form_fields).length) {
        if (form_fields[group_key]) {
          const key = `fields.${group_key}.fields`;
          const data = await this.FormRepository.findOneAndUpdate(
            { form_id: form.form_id },
            {
              $push: {
                [key]: form_field,
              },
              validationschema,
            },
            {
              new: true,
            },
          )
            .select(this.select)
            .catch((error) => {
              throw error;
            });
          if (data) {
            return form_field;
          }
          throw {
            status: HttpStatus.CONFLICT,
            message: 'Unable to add the field',
          };
        } else {
          const data = await this.createNewGroupAndUpdate(
            group_title,
            group_key,
            form_field,
            form,
            group_description,
          );
          if (data) {
            return form_field;
          }
          throw {
            status: HttpStatus.CONFLICT,
            message: 'Unable to add the field',
          };
        }
      } else {
        const data = await this.createNewGroupAndUpdate(
          group_title,
          group_key,
          form_field,
          form,
          group_description,
        );
        if (data) {
          return form_field;
        }
        throw {
          status: HttpStatus.CONFLICT,
          message: 'Unable to add the field',
        };
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add section to form by form_id
   * @param form_id string
   * @param body AddSectionDto
   * @returns Promise<FIELD>
   */
  async addSection(form_id: string, body: AddSectionDto): Promise<FIELD> {
    try {
      const form = await this.findOne({ form_id }, false, {
        form_id: true,
        fields: true,
        status: true,
        has_sub_forms: true,
        validationschema: true,
      });

      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };

      const section = await this.FormSectionsRepositoryService.findOne({
        section_id: body.section_id,
      });

      if (!section)
        throw { status: HttpStatus.NOT_FOUND, message: 'Section not found' };

      const fields: any = form.fields;

      const section_name = section.name.replace(/\s+/g, '_').toLowerCase();

      fields[section_name] = {
        group_title: section.name,
        group_key: section_name,
        group_description: section.description,
        group_index: body.group_index,
      };

      form.fields = fields;

      return await this.FormRepository.findOneAndUpdate({ form_id }, form, {
        new: true,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Remove section to form by form_id
   * @param form_id string
   * @param body RemoveSectionDto
   * @returns Promise<FIELD>
   */
  async removeSection(form_id: string, body: RemoveSectionDto): Promise<FIELD> {
    try {
      const form = await this.findOne({ form_id }, false, {
        form_id: true,
        fields: true,
        status: true,
        has_sub_forms: true,
        validationschema: true,
      });
      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };

      const section = await this.FormSectionsRepositoryService.findOne({
        section_id: body.section_id,
      });

      if (!section)
        throw { status: HttpStatus.NOT_FOUND, message: 'Section not found' };

      const fields: any = form.fields;

      const section_name = section.name.replace(/\s+/g, '_').toLowerCase();

      delete fields[section_name];

      form.fields = fields;

      return await this.FormRepository.findOneAndUpdate({ form_id }, form, {
        new: true,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Copy the field based on requested copy details
   * @param form_id string
   * @param body CopyFieldDto
   * @returns Promise<FIELD>
   */
  async copyField(form_id: string, body: CopyFieldDto): Promise<FIELD> {
    try {
      const field_id = body.field_id;
      const group_key = body.group_key;
      const form: FormsRepository = await this.findFieldInFormWithIds(
        form_id,
        field_id,
        group_key,
      );
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Field was not found in given section or form',
        };

      const copy_field: FIELD = form.fields[group_key].fields.filter(
        (field: FIELD) => field.field_id === field_id,
      )[0];

      copy_field.name = `${copy_field.name}_copy_${Date.now()}`;

      copy_field.field_id = generateUUID();

      const key = `fields.${group_key}.fields`;

      const data = await this.FormRepository.findOneAndUpdate(
        { form_id: form.form_id },
        {
          $push: {
            [key]: copy_field,
          },
          validationschema: {
            ...form.validationschema,
            [group_key]: {
              ...form.validationschema[group_key],
              [copy_field.name]: {
                type: copy_field?.input_type || 'text',
                ...copy_field.validation_schema,
              },
            },
          },
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
      if (data) {
        return copy_field;
      }
      throw {
        status: HttpStatus.CONFLICT,
        message: 'Unable to copy the field',
      };
    } catch (error) {
      throw error;
    }
  }

  addUniqueKey(obj, key, value) {
    let newKey = key;
    let counter = 1;

    while (obj.hasOwnProperty(newKey)) {
      newKey = `${key}_copy${counter}`;
      counter++;
    }

    obj[newKey] = value;
  }

  /**
   * Copy or Duplicate the section with form
   * @param form_id string
   * @param body any
   * @returns Promise<FIELD>
   */
  async duplicateSection(form_id: string, body: any): Promise<FormsRepository> {
    try {
      const group_key = body.group_key;
      const group_index = body.group_index;

      const form: FormsRepository = await this.findOne({ form_id }, false, {
        form_id: true,
        fields: true,
        status: true,
        has_sub_forms: true,
        validationschema: true,
      });

      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Field was not found in given section or form',
        };

      let newKey = group_key;
      let counter = 1;

      while (form.fields.hasOwnProperty(newKey)) {
        newKey = `${group_key}_copy${counter}`;
        counter++;
      }

      const sectionFields = [];

      let duplicateField = {
        group_title: form.fields[group_key]?.group_title,
        group_key: newKey,
        group_id: generateUUID(),
        group_description: form.fields[group_key]?.group_description,
        group_index: group_index,
        is_iterative_or_not: form.fields[group_key]?.is_iterative_or_not,
        iteration_min_length: form.fields[group_key]?.iteration_min_length,
        iteration_max_length: form.fields[group_key]?.iteration_max_length,
        fields: form.fields[group_key].fields.map((field: FIELD) => {
          const newFelidName = `${field.name}_copy_${Date.now()}`;
          sectionFields.push({
            newFelidName,
            oldFelidName: field.name,
          });
          field.name = newFelidName;
          field.field_id = generateUUID();
          return field;
        }),
      };

      let duplicateSectionValidation = {};

      sectionFields.map((field) => {
        duplicateSectionValidation[field.newFelidName] =
          form.validationschema[group_key][field.oldFelidName];
      });

      const formsRepository: FormsRepository = await this.findOne(
        { form_id },
        false,
        {
          form_id: true,
          fields: true,
          status: true,
          has_sub_forms: true,
          validationschema: true,
        },
      );

      const data = await this.FormRepository.findOneAndUpdate(
        { form_id: formsRepository.form_id },
        {
          fields: {
            ...formsRepository.fields,
            [newKey]: duplicateField,
          },
          validationschema: {
            ...formsRepository.validationschema,
            [newKey]: duplicateSectionValidation,
          },
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update field in form by field id and group_key
   * @param form_id string
   * @param body UpdateField
   * @returns Promise<FormsRepository>
   */
  async updateField(form_id: string, body: UpdateField): Promise<FIELD> {
    try {
      delete body?.name;
      const group_key = body.group_key;
      const field_id = body.field_id;
      const key = `fields.${group_key}.fields`;
      const form = await this.findFieldInFormWithIds(
        form_id,
        field_id,
        group_key,
      );
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };
      let updated_field: FIELD;
      const fields = form.fields[group_key].fields.map((field: FIELD) => {
        if (field.field_id === field_id) {
          updated_field = {
            ...field,
            ...body,
          };
          return updated_field;
        } else {
          return field;
        }
      });

      const validationschema = this.globalService.generateValidationSchema(
        form,
        group_key,
        updated_field,
      );

      const data = await this.FormRepository.findOneAndUpdate(
        { form_id },
        {
          [key]: fields,
          validationschema,
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
      if (body.auto_fill) {
        const targetField = form.fields[group_key].fields.find(
          (f) => f.field_id === field_id,
        );

        const autoFillDep = {
          enabled: body.auto_fill.enabled,
          source_form_id: form_id,
          source_form_name: form.name,
          source_section_name: form.fields[group_key].group_title,
          source_section_key: group_key,
          source_field_name: targetField.name,
          source_field_id: field_id,
        };

        const result = await this.FormRepository.aggregate([
          { $match: { form_id: body.auto_fill.source_form_id } },
          {
            $project: {
              [`field`]: {
                $filter: {
                  input: `$fields.${body.auto_fill.source_section_key}.fields`,
                  as: 'f',
                  cond: {
                    $eq: ['$$f.field_id', body.auto_fill.source_field_id],
                  },
                },
              },
            },
          },
        ]);
        const matchingField = result[0]?.field[0];
        matchingField['auto_fill_dependencies'] = [];
        if (
          matchingField['auto_fill_dependencies'] &&
          matchingField['auto_fill_dependencies'].length > 0
        ) {
          const existedField = matchingField['auto_fill_dependencies'].find(
            (f) => f.field_id === field_id,
          );

          if (!existedField) {
            matchingField['auto_fill_dependencies'].push(autoFillDep);
          }
        } else {
          matchingField['auto_fill_dependencies'].push(autoFillDep);
        }

        const update = await this.FormRepository.findOneAndUpdate(
          {
            form_id: body.auto_fill.source_form_id,
            [`fields.${body.auto_fill.source_section_key}.fields.field_id`]:
              body.auto_fill.source_field_id,
          },
          {
            $set: {
              [`fields.${body.auto_fill.source_section_key}.fields.$[elem]`]: {
                ...matchingField,
              },
            },
          },
          {
            arrayFilters: [{ 'elem.field_id': body.auto_fill.source_field_id }],
            new: true,
          },
        );
      }
      if (data) return updated_field;
      throw {
        status: HttpStatus.CONFLICT,
        message: 'Unable to update the field',
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create new field in form by group
   * @param group_title string
   * @param field FIELD
   * @param group_key string
   * @param form FormsRepository
   * @returns Promise<FormsRepository>
   */
  async createNewGroupAndUpdate(
    group_title: string,
    group_key: string,
    field: FIELD,
    form: FormsRepository,
    group_description: string,
  ): Promise<FormsRepository> {
    try {
      const group: GROUP = {
        group_title: group_title,
        group_key,
        group_id: generateUUID(),
        fields: [field],
        group_index: (form?.fields && Object.keys(form?.fields)?.length) || 0,
        group_description,
      };
      let group_validations = {
        [field.name]: {
          type: field?.input_type || 'text',
          ...field.validation_schema,
        },
      };
      if (form.validationschema && form.validationschema[group_key]) {
        group_validations = {
          ...form.validationschema[group_key],
          ...group_validations,
        };
      }
      const validationschema = {
        ...form.validationschema,
        [group_key]: group_validations,
      };
      return await this.FormRepository.findOneAndUpdate(
        { form_id: form.form_id },
        {
          fields: {
            ...form.fields,
            [group_key]: group,
          },
          validationschema,
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Patch the form details except fields
   * @param form_id string
   * @param body FormDetails
   * @returns Promise<FormsRepository>
   */
  async patchForm(
    form_id: string,
    body: FormDetails,
  ): Promise<FormsRepository> {
    try {
      const form = await this.findOne({ form_id });
      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };
      const data = await this.globalService.generateFormDetail(body, form);
      return await this.FormRepository.findOneAndUpdate({ form_id }, data, {
        new: true,
      }).catch((error) => {
        throw error;
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete the field in form by field id and group key
   * @param form_id string
   * @param body DeleteFieldDto
   * @returns Promise<boolean>
   */
  async deleteField(form_id: string, body: DeleteFieldDto): Promise<boolean> {
    try {
      const form = await this.findOne({ form_id });

      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };

      const group_key = body.group_key;

      const field_id = body.field_id;

      if (field_id) {
        const key = `fields.${group_key}.fields`;

        const form = await this.findFieldInFormWithIds(
          form_id,
          field_id,
          group_key,
        );

        if (!form)
          throw {
            status: HttpStatus.NOT_FOUND,
            message: 'Form/Field Not Found',
          };
        const dependencies = form.fields[group_key].fields.filter(
          (field: FIELD) => field.field_id === field_id,
        )[0].dependencies;

        const autoFillDep = form.fields[group_key].fields.filter(
          (field: FIELD) => field.field_id === field_id,
        )[0].auto_fill_dependencies;

        if (dependencies || autoFillDep) {
          throw {
            status: HttpStatus.BAD_REQUEST,
            message:
              'This form field are dependent on another form or this form field and cannot be deleted.',
          };
        }
        const field_key = form.fields[group_key].fields.filter(
          (field: FIELD) => field.field_id === field_id,
        )[0].name;

        const validationschema = form.validationschema;

        delete validationschema[group_key][field_key];

        return (await this.FormRepository.findOneAndUpdate(
          { form_id },
          {
            $pull: {
              [key]: { field_id },
            },
            validationschema,
          },
          {
            new: true,
          },
        ).catch((error) => {
          throw error;
        }))
          ? true
          : false;
      }
      let auto_fill_dependencies: boolean;
      let field_dependencies: boolean;
      for (const field of form.fields[group_key].fields) {
        if (field?.auto_fill_dependencies) {
          auto_fill_dependencies = true;
        }
        if (field?.dependencies) {
          field_dependencies = true;
        }
      }
      if (auto_fill_dependencies || field_dependencies) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message:
            'This form section fields are dependent on another form or this form field and cannot be deleted.',
        };
      }
      const key = `fields.${group_key}`;

      const validationKey = `validationschema.${group_key}`;

      return (await this.FormRepository.findOneAndUpdate(
        { form_id },
        {
          $unset: { [key]: null, [validationKey]: null },
        },
      ))
        ? true
        : false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check field in form or not
   * @param form_id string
   * @param field_id string
   * @param key string
   * @returns Promise<FormsRepository>
   */
  async findFieldInFormWithIds(
    form_id: string,
    field_id: string,
    group_key: string,
  ): Promise<FormsRepository> {
    const key = `fields.${group_key}.fields`;

    const find = {
      form_id,
      [key]: {
        $elemMatch: { field_id },
      },
    };

    return await this.findOne(find);
  }

  /**
   *
   * @param form_id string
   * @param body UpdateIndex
   * @returns Promise<FormsRepository>
   */
  async updateIndex(
    form_id: string,
    body: UpdateIndex,
  ): Promise<FormsRepository> {
    try {
      const form = await this.findOne({ form_id });
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };
      const group_key = body.group_key;
      const fields = body.fields;
      const group_index = body.group_index;
      const updatedData = {};
      if (group_index) {
        updatedData[`fields.${group_key}.group_index`] = group_index;
      }
      if (fields?.length) {
        const fieldIds = fields.map((field) => field.field_id);
        const fieldIndexs = fields.map((field) => field.field_index);
        const form_fields = form.fields[group_key]?.fields.map((field) => {
          if (fieldIds.includes(field.field_id)) {
            return {
              ...field,
              field_index:
                fieldIndexs[fieldIds.findIndex((id) => id === field.field_id)],
            };
          }
          return field;
        });
        updatedData[`fields.${group_key}.fields`] = form_fields;
      }
      return await this.FormRepository.findOneAndUpdate(
        { form_id },
        updatedData,
        { new: true },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
    } catch (error) {
      throw error;
    }
  }

  /**
   *
   * @param form_id string
   * @param body UpdateGroupIndex
   * @returns Promise<FormsRepository>
   */
  async updateGroupsIndex(
    form_id: string,
    body: UpdateGroupIndex,
  ): Promise<FormsRepository> {
    try {
      const form = await this.findOne({ form_id });

      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };

      const updatedData = { fields: {} };

      const groupKeys = body.groups.map((group: any) => group.group_key);

      const groupIndexes = body.groups.map((group: any) => group.group_index);

      groupKeys.map((key, index) => {
        updatedData['fields'][key] = {
          ...form.fields[key],
          group_index: groupIndexes[index],
        };
      });

      return await this.FormRepository.findOneAndUpdate(
        { form_id },
        updatedData,
        { new: true },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
    } catch (error) {
      throw error;
    }
  }

  /**
   *
   * @param body UpdateFormsIndex
   * @returns Promise<any>
   */
  async updateFormsIndex(body: UpdateFormsIndex): Promise<any> {
    try {
      return await Promise.all(
        body.forms.map(async (formIndex: FormIndex) => {
          const form = await this.findOne({ form_id: formIndex.form_id });

          if (!form)
            throw {
              status: HttpStatus.NOT_FOUND,
              message: 'Form/Field Not Found',
            };

          await this.FormRepository.findOneAndUpdate(
            { form_id: form.form_id },
            { order_position: formIndex.form_index },
            {
              returnDocument: 'after',
            },
          );
        }),
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find the forms by the condition
   * @param findBy Object "{column: value}"|| array with conditions
   * @returns Promise<FormsRepository>
   */
  async findOne(
    condition: QueryOptions<FormsRepository>,
    withDeleted = false,
    select = this.select,
    expand = null,
  ): Promise<FormsRepository> {
    try {
      if ('id' in condition) {
        condition._id = new ObjectID(condition.id);
        delete condition.id;
      }
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }
      let query = this.FormRepository.findOne(condition);
      if (expand) {
        query = query.populate(expand);
      }

      return await query.select(select).catch((error) => {
        throw error;
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the form
   * @param data FormsRepoDto
   * @param form_id string
   * @returns Promise<FormsRepository>
   */
  async update(
    data: UpdateFormsRepoDto,
    form_id: string,
  ): Promise<FormsRepository> {
    try {
      const form = await this.findOne({ form_id }, false, {
        ...this.select,
        // id: true,
      });
      if (!form) {
        throw new NotFoundException();
      }
      const groups: GROUP[] = data.groups;
      let validationschema: any = {};
      let fields: Record<string, GROUP> = {};
      groups
        .map((group: GROUP) => {
          const group_title = formatStringToKey(group.group_title);
          group.fields.forEach((field: FIELD) => {
            field.name = formatStringToKey(field.name);
            if (group_title in validationschema) {
              validationschema[group_title][field.name] = {
                type: field?.input_type || 'text',
                ...field.validation_schema,
              };
            } else {
              validationschema = {
                ...validationschema,
                [group_title]: {
                  [field.name]: {
                    type: field?.input_type || 'text',
                    ...field.validation_schema,
                  },
                },
              };
            }
          });
          return group;
        })
        .forEach((group: GROUP) => {
          const group_title = formatStringToKey(group.group_title);
          if (group_title in fields) {
            fields[group_title] = group;
          } else {
            fields = {
              ...fields,
              [group_title]: group,
            };
          }
        });
      const savedData: any = {
        name: data.name,
        fields,
        status: true,
        validationschema,
        order_position: data?.order_position ? data?.order_position : 0,
      };
      if (data.app_id) savedData.app_id = data.app_id;
      return await this.FormRepository.findOneAndUpdate(
        { form_id: form.form_id },
        savedData,
        {
          returnDocument: 'after',
        },
      ).catch((error) => {
        throw error;
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get All forms
   * @returns Promise<FormsRepository[]>
   */
  async list(
    condition: QueryOptions<FormsRepository> = { status: false },
    withDeleted = false,
    select: QueryOptions<FormsRepository> = this.select,
    url_flag: boolean = true,
  ): Promise<FormsRepository[]> {
    if (condition === null) {
      condition = { status: true };
    }
    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    } else {
      condition['deleted_at'] = { $eq: null };
    }
    if (select === null) {
      select = this.select;
    }
    const list = await this.FormRepository.find(condition)
      .select(select)
      .catch((error) => {
        throw error;
      });
    if (url_flag) {
      if (list.length) {
        return list;
      }
      throw new HttpException(
        'There are no forms to list',
        HttpStatus.NOT_FOUND,
      );
    }
    return list;
  }

  /**
   * Save Form Data
   * @param body any
   * @returns HttpException|Response
   */
  async saveFormData(
    body,
    response: Response,
    form?: FormsRepository,
    id?: any,
  ): Promise<Response> {
    try {
      const data: any = { values: body };
      if (id) {
        const value = await this.FormValueRepository.findOne({ id });

        if (!value) {
          throw new NotFoundException();
        }

        data.id = value.id;
        data.form = value.form;
      } else {
        data.form = form;
      }
      const validateErrors = await this.globalService.validate(
        data.form.validationschema,
        data.values,
        data.form.id,
      );
      data.form = form.id;
      // if (Object.keys(validateErrors).length > 0) return validateErrors;
      if (Object.keys(validateErrors).length > 0)
        throw new NotAcceptableException(validateErrors);
      if (
        await this.FormValueRepository.findByIdAndUpdate(
          { id: data.id },
          data,
          {
            upsert: true,
            returnDocument: 'after',
          },
        )
      ) {
        return response
          .status(HttpStatus.CREATED)
          .json({ message: 'Data Saved Successfully' });
      } else {
        throw new HttpException(
          'Something went wrong. Please try again later.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } catch (error) {
      return response
        .status(error.status || HttpStatus.INTERNAL_SERVER_ERROR)
        .json({
          message:
            error?.message || 'Something went wrong. Please try again later.',
          errors: error?.response,
        });
    }
  }

  /**
   * Get Form Data with from
   * @param id FormValueRepository.id
   * @returns HttpException|Response
   */
  async getFormDataWithForm(id: any) {
    try {
      /* const data = await this.FormValueRepository.findOne({
        where: {
          _id: new ObjectID(id),
        },
        withDeleted,
        relations: { form: true },
      }); */
      const [data] = await this.FormValueRepository.aggregate([
        {
          $match: { _id: new ObjectID(id) },
        },
        {
          $lookup: {
            from: 'forms_repository',
            localField: 'form',
            foreignField: '_id',
            as: 'form',
          },
        },
        {
          $unwind: {
            path: '$form',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $project: {
            _id: 0,
            id: '$_id',
            form: {
              ...this.select,
              id: '$form._id',
            },
            values: 1,
            values_encryption_status: 1,
          },
        },
      ]);
      if (!data) {
        throw new NotFoundException();
      }
      const form: any = data.form;
      const fields: any = form.fields;
      const values: any = data?.values_encryption_status
        ? this.encryptionService.decryption(data?.values)
        : data?.values;

      Object.keys(fields).forEach((field) => {
        fields[field].columns.forEach((column) => {
          column.value = values[field][column.name];
        });
      });

      form.fields = fields;

      data.form = form;

      delete data.values;

      return data;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get All form values
   * @returns FormValuesRepository[]
   */
  async valuesList(): Promise<FormValuesRepository[]> {
    return this.FormValueRepository.find({
      order: {
        updated_at: 'DESC',
        id: 'DESC',
      },
    });
  }

  /**
   * Change status of form
   * @param id number
   */
  async changeStatus(id: string, body: any) {
    try {
      const form = await this.findOne({ form_id: id });
      const fields = form.fields;
      let auto_fill_dependencies: boolean;
      for (const groupKey in fields) {
        if (fields.hasOwnProperty(groupKey)) {
          const group = fields[groupKey];

          for (const field of group.fields) {
            if (field?.auto_fill_dependencies) {
              auto_fill_dependencies = true;
            }
          }
        }
      }

      if (auto_fill_dependencies && form.status) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message:
            'This form fields are dependent on another form field and cannot be unpublish.',
        };
      }
      if (form) {
        return await this.FormRepository.findOneAndUpdate(
          {
            form_id: form.form_id,
          },
          body,
          {
            returnDocument: 'after',
          },
        );
      }
      throw new NotFoundException();
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong. Please try again later.',
        error.status || 500,
      );
    }
  }

  /**
   * Validate Preview form
   * @param form_id string
   * @param body any
   * @returns Promise<boolean | any>
   */
  async validatePreview(form_id: any, body: any): Promise<boolean | any> {
    try {
      if (Object.keys(body)?.length === 0) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: 'Please send the values',
        };
      }
      const form = await this.findOne({ form_id });
      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };
      const errors = await this.globalService.validate(
        form.validationschema,
        body,
        form,
      );
      return Object.keys(errors).length ? errors : true;
    } catch (error) {
      throw {
        status: HttpStatus.BAD_REQUEST,
        message: 'Please send the values in correct format',
      };
    }
  }
  async customValidation(
    form_id: string,
    body: CustomValidationBody,
  ): Promise<FIELD> {
    try {
      const group_key = body.group_key;
      const field_id = body.field_id;
      const key = `fields.${group_key}.fields`;
      const form = await this.findFieldInFormWithIds(
        form_id,
        field_id,
        group_key,
      );
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };
      let updated_field: FIELD;
      const fields = form.fields[group_key].fields.map((field: FIELD) => {
        if (field.field_id === field_id) {
          updated_field = {
            ...field,
            ...body,
          };
          return updated_field;
        } else {
          return field;
        }
      });

      const validationschema = this.globalService.generateValidationSchema(
        form,
        group_key,
        updated_field,
      );

      const data = await this.FormRepository.findOneAndUpdate(
        { form_id },
        {
          [key]: fields,
          validationschema,
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
      if (body.validation_schema.conditional_validation) {
        const condDependencies = {
          selectedFormId: form_id,
          selectedGroupKey: group_key,
          selectedInputId: field_id,
          conditions: body.validation_schema.conditional_validation.conditions,
        };

        const result = await this.FormRepository.aggregate([
          {
            $match: {
              form_id:
                body.validation_schema.conditional_validation.selectedFormId,
            },
          },
          {
            $project: {
              [`field`]: {
                $filter: {
                  input: `$fields.${body.validation_schema.conditional_validation.selectedGroupKey}.fields`,
                  as: 'f',
                  cond: {
                    $eq: [
                      '$$f.field_id',
                      body.validation_schema.conditional_validation
                        .selectedInputId,
                    ],
                  },
                },
              },
            },
          },
        ]);
        const matchingField = result[0]?.field[0];
        matchingField['dependencies'] = [];
        if (
          matchingField['dependencies'] &&
          matchingField['dependencies'].length > 0
        ) {
          const existedField = matchingField['dependencies'].find(
            (f) => f.field_id === field_id,
          );
          if (!existedField) {
            matchingField['dependencies'].push(condDependencies);
          }
        } else {
          matchingField['dependencies'].push(condDependencies);
        }

        const update = await this.FormRepository.findOneAndUpdate(
          {
            form_id:
              body.validation_schema.conditional_validation.selectedFormId,
            [`fields.${body.validation_schema.conditional_validation.selectedGroupKey}.fields.field_id`]:
              body.validation_schema.conditional_validation.selectedInputId,
          },
          {
            $set: {
              [`fields.${body.validation_schema.conditional_validation.selectedGroupKey}.fields.$[elem]`]:
                {
                  ...matchingField,
                },
            },
          },
          {
            arrayFilters: [
              {
                'elem.field_id':
                  body.validation_schema.conditional_validation.selectedInputId,
              },
            ],
            new: true,
          },
        );
      }
      if (data) return updated_field;
      throw {
        status: HttpStatus.CONFLICT,
        message: 'Unable to update the field',
      };
    } catch (error) {
      throw error;
    }
  }
}

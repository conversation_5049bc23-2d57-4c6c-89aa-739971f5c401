import { Module, forwardRef } from '@nestjs/common';
import { FormsRepositoryController } from './formsrepository.controller';
import {
  FormsRepository,
  FormsRepositorySchema,
} from 'src/entities/mongodb/formsrepository.entity';
import { FormsRepositoryService } from 'src/services/formsrespository.service';
import { OrganizationModule } from '../organization/organization.module';
import { MongooseModule } from '@nestjs/mongoose';
import {
  FormValuesRepository,
  FormValuesRepositorySchema,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import {
  ModifiedFormFieldsRepository,
  ModifiedFormFieldsRepositorySchema,
} from 'src/entities/mongodb/modifiedformfieldsrepository.entity';
import {
  AutoFillKeysExistsRule,
  ConditionalValidationKeysExistsRule,
  CustomValidationKeysExistsRule,
  FieldIdExistRule,
  FormsExistRule,
  HasSubFormsExistRule,
  SourceFieldIdExistsRule,
  SourceFormIdExistsRule,
  SourceSectionKeyExistsRule,
} from 'src/util/validations/forms.validations';
import { AppsModule } from '../apps/apps.module';
import { FormFieldsModule } from '../formfields/formfields.module';
import { AdminModule } from '../admin/admin.module';
import { GlobalService } from 'src/services/global.service';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';
import {
  FormSectionsRepository,
  FormSectionsRepositorySchema,
} from 'src/entities/mongodb/form-sections.entity';
import { UserService } from 'src/services/user.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/entities/user.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { FormValueService } from 'src/services/formvalues.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { ConfigurationsService } from 'src/services/configurations.service';
import { Organization } from 'src/entities/organization.entity';
import { HttpModule } from '@nestjs/axios';
import { Configurations } from 'src/entities/configurations.entity';
import { SendGridService } from 'src/util/sendgrid.service';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import EncryptionService from 'src/services/encryption.service';
import { OrganizationService } from 'src/services/organization.service';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { PaginationService } from 'src/util/pagination.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import { InjectRequestInterceptor } from 'src/util/interceptor/request.interceptor';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { CaregiversService } from 'src/services/caregivers.service';
import { Caregivers } from 'src/entities/caregivers.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: FormsRepository.name, schema: FormsRepositorySchema },
      {
        name: ModifiedFormFieldsRepository.name,
        schema: ModifiedFormFieldsRepositorySchema,
      },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      {
        name: FormSectionsRepository.name,
        schema: FormSectionsRepositorySchema,
      },
    ]),
    TypeOrmModule.forFeature(
      [
        User,
        OrgAppConfiguration,
        OnboardingEmployee,
        Configurations,
        Organization,
        IndustryAppProcess,
        IndustryTypes,
        Caregivers,
      ],
      'mysql',
    ),
    forwardRef(() => OrganizationModule),
    forwardRef(() => AppsModule),
    AdminModule,
    FormFieldsModule,
    HttpModule,
  ],
  exports: [
    FormsRepositoryService,
    MongooseModule.forFeature([
      { name: FormsRepository.name, schema: FormsRepositorySchema },
      {
        name: ModifiedFormFieldsRepository.name,
        schema: ModifiedFormFieldsRepositorySchema,
      },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      {
        name: FormSectionsRepository.name,
        schema: FormSectionsRepositorySchema,
      },
      {
        name: OnBoardEmpChecklistRepository.name,
        schema: OnBoardEmpChecklistRepositorySchema,
      },
      {
        name: OnBoardOrgChecklistRepository.name,
        schema: OnBoardOrgChecklistRepositorySchema,
      },
    ]),
  ],
  providers: [
    FormsRepositoryService,
    FormSectionsRepositoryService,
    FormsExistRule,
    HasSubFormsExistRule,
    GlobalService,
    UserService,
    OnboardingEmployeeService,
    FormValueService,
    ClientsRepositoryService,
    ConfigurationsService,
    SendGridService,
    EncryptionService,
    OrganizationService,
    IndustryTypesService,
    PaginationService,
    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
    CaregiversService,
    FieldIdExistRule,
    CustomValidationKeysExistsRule,
    ConditionalValidationKeysExistsRule,
    AutoFillKeysExistsRule,
    SourceFormIdExistsRule,
    SourceSectionKeyExistsRule,
    SourceFieldIdExistsRule,
    {
      provide: APP_INTERCEPTOR,
      useClass: InjectRequestInterceptor,
    },
  ],
  controllers: [FormsRepositoryController],
})
export class FormsRepositoryModule {}

import { Injectable } from '@nestjs/common';
import {
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { IndustryTypesService } from 'src/services/industry-types.service';

@ValidatorConstraint({ name: 'IndustryTypesExists', async: true })
@Injectable()
export class IndustryTypesExistsRule implements ValidatorConstraintInterface {
  constructor(private IndustryTypesService: IndustryTypesService) {}

  async validate(value: any) {
    try {
      return (await this.IndustryTypesService.findOne(
        {
          industry_type_id: value,
        },
        false,
      ))
        ? true
        : false;
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Industry Type doesn't exist`;
  }
}

export function IndustryTypesExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IndustryTypesExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: IndustryTypesExistsRule,
    });
  };
}

import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';

import { Organization } from './organization.entity';

@Entity()
export class Caregivers {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  caregiver_id: string;

  @Column()
  name: string;

  @Column()
  email: string;

  @Column()
  mobile_number: string;

  @Column()
  password: string;

  @Column('simple-array', { nullable: true })
  clients: string[];

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

import {
  Controller,
  Get,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
  HttpException,
  HttpStatus,
  Put,
  UseGuards,
  Req,
  Param,
  Res,
  Patch,
  Query,
  Delete,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request, Response } from 'express';

import {
  AddFormOrganization,
  AddFormsOrganization,
  OrganizationRegisterDto,
  OrganizationUpdateDto,
  RemoveFormOrganization,
  UpdateFormsStatusDto,
} from 'src/dto/organization.dto';
import { AdminAuth } from 'src/guards/admin.guard';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { OrganizationService } from 'src/services/organization.service';
import { AdminOrOrganizationAuth } from 'src/guards/adminOrOrganization.guard';
import { Apps } from 'src/entities/apps.entity';
import { OrgAppConfigurationService } from 'src/services/org-app-configuration.service';

@Controller('organization')
export default class OrganizationController {
  constructor(
    private organizationService: OrganizationService,
    private OrgAppConfigurationService: OrgAppConfigurationService,
  ) {}

  @Post('/')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), AdminAuth)
  async registerOrganization(
    @Body() body: OrganizationRegisterDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.organizationService.createOrganization(body);
      return data
        ? response.send({
            status: true,
            message: 'Organization Onboard Successfully',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to Onboard the Organization',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getOrganizations(@Res() response: Response): Promise<Response> {
    try {
      const data = await this.organizationService.list();

      return response.send({
        status: true,
        message: 'Organization list fetched successfully',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/public')
  async getOrganizationsForPublicAccess(
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let organizations: any = await this.organizationService.list(null, {
        id: true,
        organization_id: true,
        name: true,
        apps: false,
      });

      if (organizations.length) {
        organizations = await Promise.all(
          organizations.map(async (organization) => {
            if (organization?.apps?.length) {
              organization.apps = await Promise.all(
                organization.apps.map(async (app) => {
                  app.orgAppConfiguration =
                    await this.OrgAppConfigurationService.findOne(
                      {
                        app: { id: app.id },
                        organization: { id: organization.id },
                      },
                      false,
                      null,
                      { app: true, organization: true },
                    );
                  delete app.id;
                  return app;
                }),
              );
            }
            delete organization.id;
            return organization;
          }),
        );
      }

      return response.send({
        status: true,
        message: 'Success',
        data: organizations,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:organization_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getOrganization(
    @Param('organization_id') organization_id: string,
    @Query('relation') relation: string,
    @Query('onboarding_apps') onboarding_apps: boolean = false,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let relations = [];
      const relationObj = {};
      if (relation) {
        relations = relation.split(',');
        relations.forEach((relation) => {
          if (relation == 'apps') {
            relationObj[relation] = { industry_app_process: true };
          } else {
            relationObj[relation] = true;
          }
        });
      }

      const data = await this.organizationService.findOne(
        { organization_id },
        false,
        { ...this.organizationService.select, id: true },
        relationObj,
      );

      if (data.apps && data.apps.length && !onboarding_apps) {
        const onboardingApps = ['HC_OBRD'];

        data.apps = data.apps.filter(
          (app: Apps) =>
            !onboardingApps.includes(app?.industry_app_process?.process_code),
        );
      }

      delete data.id;
      return data
        ? response.send({
            status: true,
            message: 'Organization get successfully',
            data,
          })
        : response.status(HttpStatus.NOT_FOUND).send({
            status: true,
            message: 'Organization Not Found',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/:id?')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async updateOrganization(
    @Body() body: OrganizationUpdateDto,
    @Req() req: Request,
    @Res() response: Response,
    @Param('id') id?: string,
  ): Promise<Response> {
    try {
      let userId = id;

      if (req.user['isOrganization']) userId = req.user['organization_id'];

      if (!userId)
        throw new HttpException(
          'Please check the accessed url. Must pass the organization id in url',
          HttpStatus.BAD_REQUEST,
        );

      return await this.organizationService.updateOrganization(
        body,
        userId,
        response,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Add Multiple Form to Organization
   * @param req Request
   * @param body AddFormOrganization
   * @param response Response
   * @param organiztion_id string
   * @returns Promise<Response>
   */
  @Patch('add-forms/?:organization_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async addFormsOrganization(
    @Req() req: Request,
    @Body() body: AddFormsOrganization,
    @Res() response: Response,
    @Param('organization_id') organization_id?: string,
  ): Promise<Response> {
    try {
      let id = organization_id;

      const organization = await this.organizationService.findOne({
        organization_id,
      });

      if (!organization) {
        throw new HttpException(
          'Organization not found',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (req.user['isOrganization']) id = req.user['organization_id'];

      if (!id)
        throw new HttpException(
          'Please check the accessed url. Must pass the organization id in url',
          HttpStatus.BAD_REQUEST,
        );
      const data = await this.organizationService.addFormsToOrganization(
        id,
        body,
        req.user,
      );
      return data
        ? response.send({
            status: true,
            message: 'Forms added to Organization successfully',
            data,
          })
        : response.send({
            status: false,
            message: 'Unable to add forms to Organization',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Remove Form to Organization
   * @param req Request
   * @param body AddFormOrganization
   * @param response Response
   * @param organiztion_id string
   * @returns Promise<Response>
   */
  @Patch('remove-form/?:organiztion_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async removeFormOrganization(
    @Req() req: Request,
    @Body() body: RemoveFormOrganization,
    @Res() response: Response,
    @Param('organiztion_id') organiztion_id?: string,
  ): Promise<Response> {
    try {
      let id = organiztion_id;
      if (req.user['isOrganization']) id = req.user['organization_id'];
      if (!id)
        throw new HttpException(
          'Please check the accessed url. Must pass the organization id in url',
          HttpStatus.BAD_REQUEST,
        );
      const data = await this.organizationService.removeFormToOrganization(
        organiztion_id,
        body,
        req.user,
      );
      return data
        ? response.send({
            status: true,
            message: 'Form remove from Organization successfully',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to remove form to Organization',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Add Form to Organization
   * @param req Request
   * @param body AddFormOrganization
   * @param response Response
   * @param organiztion_id string
   * @returns Promise<Response>
   */
  @Patch('add-form/?:organiztion_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async addFormOrganization(
    @Req() req: Request,
    @Body() body: AddFormOrganization,
    @Res() response: Response,
    @Param('organiztion_id') organiztion_id?: string,
  ): Promise<Response> {
    try {
      let id = organiztion_id;
      if (req.user['isOrganization']) id = req.user['organization_id'];
      if (!id)
        throw new HttpException(
          'Please check the accessed url. Must pass the organization id in url',
          HttpStatus.BAD_REQUEST,
        );
      const data = await this.organizationService.addFormToOrganization(
        organiztion_id,
        body,
        req.user,
      );
      return data
        ? response.send({
            status: true,
            message: 'Form add to Organization successfully',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to add form to Organization',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch('/update-forms-status')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  @UsePipes(ValidationPipe)
  async updateFormsStatus(
    @Body() body: UpdateFormsStatusDto,
    @Res() response: Response,
    @Req() request: Request,
  ) {
    try {
      const data = await this.organizationService.updateFormsStatus(body);
      return data
        ? response.send({
            status: true,
            message: 'Form status Updated successfully',
          })
        : response.status(HttpStatus.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Unable to change the status',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('/send-email')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async sendCommonEmail(
    @Body() body: { email: string; content: string; subject: string },
    @Req() req: Request,
  ) {
    try {
      return this.organizationService.sendMail(
        req.user['organization_id'],
        body,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  //Remove organization
  @Delete('/:organization_id')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async removeOrganization(
    @Param('organization_id') organization_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.organizationService.removeOrganization(
        organization_id,
      );
      return data
        ? response.send({
            status: true,
            message: 'Organization removed successfully',
          })
        : response.status(HttpStatus.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Unable to remove the Organization',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

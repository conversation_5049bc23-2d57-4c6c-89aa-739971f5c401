import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { AdminService } from 'src/services/admin.service';

@Injectable()
export class AdminAuth implements CanActivate {
  constructor(private adminService: AdminService) {}
  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    if (user.isAdmin) {
      return (await this.adminService.findOne({ admin_id: user.admin_id }))
        ? true
        : false;
    }
    throw new UnauthorizedException();
  }
}

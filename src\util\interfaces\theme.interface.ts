export interface THEME {
  bodyFont: string;
  titleFont: string;
  linkColor: string;
  textColor: string;
  buttonColor: string;
  backgroundColor: string;
  buttonTextColor: string;
  navigationColor: string;
  buttonStyle: BUTTON_STYLE;
  navigationTextColor: string;
  sectionViewType: SECTION_VIEW_TYPE;
  mobileLayout: MOBILE_LAYOUT;
  tabletLayout: TABLET_LAYOUT;
  headerImage?: string;
}

export enum SECTION_VIEW_TYPE {
  plain = 'plain',
  accordion = 'accordion',
  stepper = 'stepper',
}

export enum MOBILE_LAYOUT {
  singleColumn = 'singleColumn',
  doubleColumn = 'doubleColumn',
}

export enum TABLET_LAYOUT {
  singleColumn = 'singleColumn',
  doubleColumn = 'doubleColumn',
}

export enum BUTTON_STYLE {
  // solid = 'solid',
  // outline = 'outline',
  rounded = 'rounded',
  standard = 'standard',
}

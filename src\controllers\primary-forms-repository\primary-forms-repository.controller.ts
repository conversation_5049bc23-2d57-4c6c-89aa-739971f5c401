import {
  <PERSON>,
  Get,
  HttpException,
  HttpStatus,
  UseGuards,
  Param,
  Res,
  Req,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response, Request } from 'express';

import { UserAuth } from 'src/guards/user.guard';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { PrimaryFormsRepositoryService } from 'src/services/primary-forms-repository.service';

@Controller('primary-forms-repository')
export default class PrimaryFormsRepositoryController {
  constructor(
    private primaryFormsRepositoryService: PrimaryFormsRepositoryService,
    private orgFormsRepositoryService: OrgFormsRepositoryService,
  ) {}

  /**
   * get list primary form details from App and organization
   * @param response Response
   * @returns Promise<Response>
   */
  @Get()
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async find(
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<Response> {
    try {
      return response.send({
        status: true,
        message: 'Form details fetched Successfully',
        data: await this.primaryFormsRepositoryService.find({
          organization_id: request.user['organization'],
          app_id: request.user['app_id'],
        }),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * get primary form details from App by primary_form_id (Optional)
   * @param primary_form_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/primary-form')
  @UseGuards(AuthGuard('jwt'))
  async findOne(
    @Res() response: Response,
    @Req() request: Request,
    @Query('app_id') app_id: string,
    @Query('primary_form_id') primary_form_id?: string,
  ): Promise<Response> {
    try {
      return response.send({
        status: true,
        message: 'Form details fetched successfully',
        data: await this.orgFormsRepositoryService.getUserPrimaryForm(
          request.user,
          app_id,
          primary_form_id,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

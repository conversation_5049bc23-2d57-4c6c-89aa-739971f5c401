import * as bodyParser from 'body-parser';
import { NestFactory } from '@nestjs/core';
import { useContainer } from 'class-validator';
import { AppModule } from './app.module';

process.setMaxListeners(100);

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    cors: {
      origin: '*',
      credentials: false,
    },
  });

  app.use(bodyParser.json({ limit: process.env.POST_JSON_LIMIT }));

  app.use(
    bodyParser.urlencoded({
      limit: process.env.POST_URL_LIMIT,
      extended: true,
    }),
  );

  useContainer(app.select(AppModule), { fallbackOnErrors: true });

  await app.listen(process.env.HOST_PORT);
}

bootstrap();

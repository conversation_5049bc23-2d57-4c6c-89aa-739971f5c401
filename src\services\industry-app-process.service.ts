import { HttpStatus, HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { FindOptionsSelect, FindOptionsWhere, Repository } from 'typeorm';

@Injectable()
export class IndustryAppProcessService {
  constructor(
    @InjectRepository(IndustryAppProcess, 'mysql')
    private IndustryAppProcessRepository: Repository<IndustryAppProcess>,
  ) {}

  public select: FindOptionsSelect<IndustryAppProcess> = {
    id: false,
    industry_app_process_id: true,
    process_name: true,
    process_code: true,
    description: true,
    status: true,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  async findOne(
    findBy: FindOptionsWhere<IndustryAppProcess>,
    withDeleted = false,
    select = this.select,
  ): Promise<IndustryAppProcess> {
    try {
      return await this.IndustryAppProcessRepository.findOne({
        where: findBy,
        withDeleted,
        select,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find Industry Types
   * @param findBy Object "{column: value}"
   * @returns Promise<IndustryTypes[]>
   */

  async find(
    findBy: FindOptionsWhere<IndustryAppProcess>,
    withDeleted = false,
    select = this.select,
  ): Promise<IndustryAppProcess[]> {
    try {
      return await this.IndustryAppProcessRepository.find({
        where: findBy,
        withDeleted,
        relations: {
          apps: true,
          industry_types: true,
        },
        select,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

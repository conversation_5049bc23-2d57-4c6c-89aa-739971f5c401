import * as _ from 'lodash';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  HttpStatus,
  Inject,
  Injectable,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';

import {
  AutoCreateFormsRepoDto,
  FormDetails,
} from 'src/dto/formrepository.dto';
import {
  AUTO_CREATE_FORM,
  FIELD,
  GROUP,
  QUIZ_FIELD,
} from 'src/util/interfaces/forms.interface';
import { AppsService } from './apps.service';
import { generateUUID, trimString } from 'src/util';
import { INPUT_TYPES_REGEX } from 'src/util/constants';
import { FormFieldsService } from './formfields.service';
import {
  FormValuesRepository,
  FormValuesRepositoryDocument,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import { OrganizationService } from './organization.service';
import { FormsRepository } from 'src/entities/mongodb/formsrepository.entity';
import { OrgFormsRepository } from 'src/entities/mongodb/orgformsrepository.entity';
import { ClientsRepositoryService } from './clientsrepository.service';
import { CaregiversService } from './caregivers.service';

@Injectable()
export class GlobalService {
  constructor(
    private readonly appsService: AppsService,
    private readonly fieldService: FormFieldsService,
    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,
    @InjectModel(FormValuesRepository.name)
    private readonly FormValueRepository: Model<FormValuesRepositoryDocument>,
    private readonly clientsRepositoryService: ClientsRepositoryService,
    @Inject(forwardRef(() => CaregiversService))
    private readonly caregiversService: CaregiversService,
  ) {}
  /**
   * Create Structure of Auto Create Form
   * @param body AutoCreateFormsRepoDto
   * @param organization string
   * @returns Promise<AUTO_CREATE_FORM>
   */
  async generateAutoCreateFormData(
    body: AutoCreateFormsRepoDto,
    organization: string = null,
  ): Promise<AUTO_CREATE_FORM> {
    const data: AUTO_CREATE_FORM = {
      form_id: generateUUID(),
      name: body.name ? trimString(body.name) : 'Untitled Form',
      description: trimString(body?.description),
      app_id: body.app_id,
      has_sub_forms: body.has_sub_forms || false,
      is_sub_form: body.is_sub_form || false,
      is_quiz_form: body?.is_quiz_form || false,
      status: false,
    };

    if (!data.app_id) {
      let app_id;

      if (organization) {
        const apps = (
          await this.organizationService.findOne(
            {
              organization_id: organization,
            },
            false,
            { id: true },
            { apps: true },
          )
        ).apps;
        app_id =
          apps.filter((app) => app.name?.toLowerCase() == 'Assessment App')[0]
            ?.app_id || apps[0]?.app_id;
      } else {
        app_id =
          (await this.appsService.findOne({ name: 'Assessment App' }))
            ?.app_id || (await this.appsService.list())[0]?.app_id;
      }
      if (!app_id) {
        throw {
          status: HttpStatus.CONFLICT,
          message: 'Before creating forms please create at least one App.',
        };
      }
      data.app_id = app_id;
    }

    if (data.has_sub_forms === false) {
      let findCondition: any = {};

      if (body?.is_quiz_form) {
        findCondition.name = {
          $regex: new RegExp('Multiple Choice'),
          $options: 'i',
        };
        findCondition.is_quiz_field = true;
      } else {
        findCondition.name = {
          $regex: new RegExp('Text'),
          $options: 'i',
        };
      }

      const fields =
        (await this.fieldService.findOne(findCondition)) ||
        (await this.fieldService.list(null, false, null, false))[0];

      if (!fields) {
        throw {
          status: HttpStatus.CONFLICT,
          message: 'Before creating forms please create at least one field.',
        };
      }

      let field: FIELD | QUIZ_FIELD = {
        ...fields.skelton,
        field_index: 0,
        name: `field_one_${Date.now()}`,
        field_id: generateUUID(),
        original_field_id: fields.field_id,
        validation_schema: {
          required: false,
          unique: false,
        },
        is_quiz_field: body?.is_quiz_form || undefined,
      };

      const section: GROUP = {
        group_title: 'Section Title',
        group_key: 'section_one',
        group_id: generateUUID(),
        group_description: '',
        group_index: 0,
        fields: [field],
      };

      data['fields'] = {
        section_one: section,
      };
      data['validationschema'] = {
        section_one: {
          [field.name]: {
            ...field.validation_schema,
            type: field?.input_type || 'text',
          },
        },
      };
    }
    return data;
  }

  /**
   *
   * @param data FormDetails
   * @param form OrgFormsRepository | FormsRepository
   * @returns
   */
  async generateFormDetail(
    data: FormDetails,
    form: OrgFormsRepository | FormsRepository,
  ): Promise<any> {
    const group = data.group;
    if (group) {
      const group_key = group.key;
      const group_title = group.title;
      const group_index = group.index;
      const group_description = group.description || null;
      const is_iterative_or_not = group.is_iterative_or_not || false;
      const iteration_min_length = group.iteration_min_length || 1;
      const iteration_max_length = group.iteration_max_length || 2;

      if (form.fields[group_key]) {
        const query = `fields.${group_key}.group_title`;
        data[query] = group_title;
        if (group_index) data[`fields.${group_key}.group_index`] = group_index;
        data[`fields.${group_key}.group_description`] = group_description;
        data[`fields.${group_key}.is_iterative_or_not`] = is_iterative_or_not;
        data[`fields.${group_key}.iteration_min_length`] = iteration_min_length;
        data[`fields.${group_key}.iteration_max_length`] = iteration_max_length;
      } else {
        data['fields'] = {
          ...form.fields,
          [group_key]: {
            group_index: (form.fields && Object.keys(form.fields)?.length) || 0,
            group_key,
            group_title,
            group_description,
            is_iterative_or_not,
            iteration_min_length,
            iteration_max_length,
          },
        };
      }
    }
    return data;
  }

  generateValidationSchema(
    form: OrgFormsRepository | FormsRepository,
    group_key: string,
    form_field: FIELD,
  ) {
    let validationschema = { ...form.validationschema };
    if (Object.keys(validationschema).length) {
      if (validationschema[group_key]) {
        validationschema[group_key] = {
          ...validationschema[group_key],
          [form_field.name]: {
            type: form_field?.input_type || 'text',
            ...form_field.validation_schema,
          },
        };
      } else {
        validationschema[group_key] = {
          [form_field.name]: {
            type: form_field?.input_type || 'text',
            ...form_field.validation_schema,
          },
        };
      }
    } else {
      validationschema = {
        [group_key]: {
          [form_field.name]: {
            type: form_field?.input_type || 'text',
            ...form_field.validation_schema,
          },
        },
      };
    }
    return validationschema;
  }

  async fieldValidate(
    form: FormsRepository | OrgFormsRepository,
    validation_schema: any,
    values: any,
    organization = null,
    existedValueId: string = null,
    group_key: string,
  ) {
    const error = {};
    for (const validation_key in validation_schema) {
      const field = form.fields[group_key]?.fields?.filter(
        (field: FIELD) => field.name === validation_key,
      )[0];
      // if (validation_key in field_values) {
      const validation = validation_schema[validation_key];
      const value = values[validation_key];
      if (Object.keys(validation).length) {
        if (
          validation?.required &&
          field?.input_type === 'file' &&
          field?.type === 'input'
        ) {
          if (typeof value == 'string') {
            error[
              validation_key
            ] = `Should be object! name, file, mimetype is required`;
          } else {
            ['name', 'file', 'mimetype'].every((key) => {
              if (!Object.keys(value).includes(key)) {
                error[validation_key] = `${key} is required`;
              }
            });
          }
        }

        if (validation.required && !value) {
          error[validation_key] = `Field is required`;
        } else if (
          validation.unique &&
          field?.type?.toLowerCase() === 'input'
        ) {
          const condition = {
            [`values.${group_key}.${validation_key}`]: {
              $regex: new RegExp(`^${value}$`),
              $options: 'i',
            },
            form: form['_id'],
          };
          if (organization) {
            condition['organization_id'] = organization;
          }
          if (existedValueId) {
            condition['form_value_id'] = { $ne: existedValueId };
          }
          const data = await this.FormValueRepository.findOne(condition);
          if (data) {
            error[validation_key] = 'Given value was existed';
          }
        } else {
          const regEx = INPUT_TYPES_REGEX[validation?.type];
          if (regEx) {
            if (value) {
              if (!regEx.test(value)) {
                error[validation_key] = 'Please enter a valid data';
              }
            }
          }
        }
      }
      // } else {
      //   if (key in errors) {
      //     errors[key][validation_key] = 'Field is required';
      //   } else {
      //     errors[key] = {
      //       [validation_key]: 'Field is required',
      //     };
      //   }
      // }
    }
    return error;
  }

  /**
   * Validate the iterative field or section based on the form <FormsRepository>
   * @param schema FormsRepository.validationschema
   * @param reqValues body
   * @param form FormsRepository.id
   * @returns errors boolean || string
   */
  async validateTheIterativeForm(
    form: FormsRepository | OrgFormsRepository,
    reqValues: any,
    key: string,
    organization = null,
    existedValueId: string = null,
    validation_key?: string,
  ) {
    const field = validation_key
      ? form.fields[key][validation_key]
      : form.fields[key];
    const field_values = validation_key
      ? reqValues[key][validation_key]
      : reqValues[key];
    const validation_schema = validation_key
      ? form.validationschema[key][validation_key]
      : form.validationschema[key];

    const fieldMaxLength = field.iteration_max_length || field_values.length;

    if (!_.isArray(field_values)) {
      return `Section/Field should be an array because it's iterative`;
    } else if (
      field_values.length < field.iteration_min_length ||
      field_values.length > fieldMaxLength
    ) {
      return `Section/Field should be between minimum-${field.iteration_min_length} and maximum-${field.iteration_max_length}`;
    } else {
      if (validation_key) {
      } else {
        const errors = {};
        for (let i = 0; i < field_values.length; i++) {
          const values = field_values[i];
          const error = await this.fieldValidate(
            form,
            validation_schema,
            values,
            organization,
            existedValueId,
            key,
          );
          /*  for (const validation_key in validation_schema) {
            // if (validation_key in field_values) {
            const validation = validation_schema[validation_key];
            const value = values[validation_key];
            if (Object.keys(validation).length) {
              if (validation.required && !value) {
                error[validation_key] = `Field is required`;
              } else if (validation.unique) {
                const condition = {
                  [`values.${key}.${validation_key}`]: {
                    $regex: new RegExp(`^${value}$`),
                    $options: 'i',
                  },
                  form: form['_id'],
                };
                if (organization) {
                  condition['organization_id'] = organization;
                }
                if (existedValueId) {
                  condition['form_value_id'] = { $ne: existedValueId };
                }
                const data = await this.FormValueRepository.findOne(condition);
                if (data) {
                  error[validation_key] = 'Given value was existed';
                }
              } else {
                const regEx = INPUT_TYPES_REGEX[validation?.type];
                if (regEx) {
                  if (value) {
                    if (!regEx.test(value)) {
                      if (key in error) {
                        error[key] = 'Please enter a valid data';
                      } else {
                        error[validation_key] = 'Please enter a valid data';
                      }
                    }
                  }
                }
              }
            }
            // } else {
            //   if (key in errors) {
            //     errors[key][validation_key] = 'Field is required';
            //   } else {
            //     errors[key] = {
            //       [validation_key]: 'Field is required',
            //     };
            //   }
            // }
          } */
          if (Object.keys(error).length) {
            errors[`${key}.${i}`] = error;
          }
        }
        return errors;
      }
    }
  }

  /**
   * Validate the response data based on the form <FormsRepository>
   * @param schema FormsRepository.validationschema
   * @param reqValues body
   * @param form FormsRepository.id
   * @returns errors object
   */
  async validate(
    schema: any,
    reqValues: any,
    form: FormsRepository | OrgFormsRepository,
    existedValueId: string = null,
    organization = null,
  ): Promise<boolean | any> {
    let errors = {};
    for (const key in schema) {
      if (form.fields[key].is_iterative_or_not) {
        const group_errors = await this.validateTheIterativeForm(
          form,
          reqValues,
          key,
          organization,
          existedValueId,
        );
        errors = group_errors;
      } else {
        const field_values = reqValues[key];
        const validation_schema = schema[key];
        if (field_values === undefined) {
          errors[key] = 'Bad Request';
        } else {
          const group_errors = await this.fieldValidate(
            form,
            validation_schema,
            field_values,
            organization,
            existedValueId,
            key,
          );
          if (Object.keys(group_errors).length) errors[key] = group_errors;
        }
      }
    }
    return errors;
  }

  /**
   * Validate the response data based on the form <FormsRepository>
   * @param schema FormsRepository.validationschema
   * @param reqValues body
   * @param form FormsRepository.id
   * @returns errors object
   */
  async validateAndEliminationOfUnknownValues(
    schema: any,
    reqValues: any,
    form: FormsRepository | OrgFormsRepository,
    existedValueId: string = null,
    organization = null,
  ): Promise<boolean | any> {
    let errors = {};

    //TODO:: Have to check why this added
    // for (const sectionKey in reqValues) {
    //   if (!schema[sectionKey]) {
    //     delete reqValues[sectionKey];
    //   } else {
    //     for (const fieldKey in reqValues[sectionKey]) {
    //       if (!schema[sectionKey][fieldKey]) {
    //         delete reqValues[sectionKey][fieldKey];
    //       }
    //     }
    //   }
    // }

    for (const key in schema) {
      if (form.fields[key].is_iterative_or_not) {
        const group_errors = await this.validateTheIterativeForm(
          form,
          reqValues,
          key,
          organization,
          existedValueId,
        );
        errors = group_errors;
      } else {
        if (reqValues[key]) {
          const field_values = reqValues[key];
          const validation_schema = schema[key];
          const group_errors = await this.fieldValidate(
            form,
            validation_schema,
            field_values,
            organization,
            existedValueId,
            key,
          );
          if (Object.keys(group_errors).length) errors[key] = group_errors;
        }
      }
    }
    return { reqValues, errors };
  }

  /**
   * Get Clients List based on assigned clients to Caregiviers based on login
   * @param user_id string
   * @param organization_id string
   * @return Promise<ClientsRepositoryService[]>
   */
  async getClientsForClientsListInput(
    user_id: string,
    organization_id: string,
  ) {
    const organization = await this.organizationService.findOne(
      { organization_id },
      false,
      { id: true },
    );
    const caregiver = await this.caregiversService.findOne(
      {
        caregiver_id: user_id,
        organization,
      },
      false,
      { id: true, clients: true },
    );
    if (!caregiver) {
      throw new UnauthorizedException();
    }
    const condition: QueryOptions<ClientsRepositoryService> = {
      status: true,
      organization_id,
      client_id: { $in: caregiver.clients },
    };
    const clients = await this.clientsRepositoryService.findAll(
      condition,
      false,
      { client_id: true, name: true, mobile_number: true },
    );
    return clients;
  }
}

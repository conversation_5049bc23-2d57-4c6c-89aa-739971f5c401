import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { error } from 'console';
import { AppsService } from 'src/services/apps.service';
import { CaregiversService } from 'src/services/caregivers.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OrganizationService } from 'src/services/organization.service';
import { UserService } from 'src/services/user.service';

@Injectable()
export class UserOrOrganizationAuth implements CanActivate {
  constructor(
    private userService: UserService,
    private appService: AppsService,
    private organizationService: OrganizationService,
    private onBoardingEmployeeService: OnboardingEmployeeService,
    private caregiversService: CaregiversService,
  ) {}
  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();

    const user = request.user;

    if (user.isOrganization) {
      return (await this.organizationService.findOne({
        organization_id: user.organization_id,
        email: user.email,
      }))
        ? true
        : false;
    }

    user.app_code = request.headers['kleza-app-code'];

    user.organization = user?.organization?.organization_id;

    const appCode = user.app_code;

    let dbUser;

    const onboardingApps = ['HC_OBRD'];

    const caregiverApps = ['HC_CARE'];

    const app = await this.appService.findOne(
      { app_code: appCode },
      false,
      { id: true },
      { industry_app_process: true },
    );

    if (user && app) {
      if (onboardingApps.includes(app.industry_app_process.process_code)) {
        dbUser = await this.onBoardingEmployeeService.findOne(
          {
            email: user?.email,
            onboarding_employee_id: user?.user_id,
          },
          false,
          { email: true, id: true },
        );
        if (dbUser) {
          return true;
        }
      } else if (
        caregiverApps.includes(app.industry_app_process.process_code)
      ) {
        dbUser = await this.caregiversService.findOne(
          {
            email: user?.email,
            caregiver_id: user?.user_id,
          },
          false,
          { email: true, id: true },
        );
        if (dbUser) {
          return true;
        }
      } else {
        dbUser = await this.userService
          .findOne(
            {
              user_id: user?.user_id,
              email: user.email,
            },
            false,
            null,
            {
              apps: true,
              organization: true,
            },
          )
          .catch((error) => {
            console.log(error);
          });
        if (dbUser) {
          const checkingAppCode = dbUser.apps.filter(
            (app) => app.app_code === appCode,
          );
          if (checkingAppCode.length) {
            user.app_id = checkingAppCode[0].app_id;
            return true;
          }
          throw new UnauthorizedException();
        }
      }

      if (dbUser) {
        const checkingAppCode = dbUser.apps.filter(
          (app) => app.app_code === appCode,
        );
        if (checkingAppCode.length) {
          user.app_id = checkingAppCode[0].app_id;
          return true;
        }
        throw new UnauthorizedException();
      }
    }
    throw new UnauthorizedException();
  }
}

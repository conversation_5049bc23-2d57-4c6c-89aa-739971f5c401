import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import {
  SchemaOptions,
  SchemaTypes,
  ObjectId,
  HydratedDocument,
} from 'mongoose';
import { OrgFormsRepository } from './orgformsrepository.entity';

const formResponseAsEmailRepoSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'form_response_as_email_repository',
  versionKey: false,
};

@Schema(formResponseAsEmailRepoSchemaOptions)
export class FormResponseAsEmailRepository {
  @Prop({ type: SchemaTypes.ObjectId })
  id: ObjectId;

  @Prop({ type: SchemaTypes.String, unique: true, required: true })
  form_res_as_email_id: string;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'OrgFormsRepository' })
  form: OrgFormsRepository;

  @Prop({ type: SchemaTypes.String, required: false })
  from_email: string;

  @Prop({ type: SchemaTypes.Boolean, required: true, default: true })
  sent_to_user: boolean;

  @Prop({ type: SchemaTypes.String, required: false })
  sent_to_user_or_other: string;

  @Prop({ type: SchemaTypes.Mixed, required: false })
  cc_emails: any;

  @Prop({ type: SchemaTypes.Boolean, default: true })
  status: boolean;

  @Prop({ type: SchemaTypes.Date })
  deleted_at: Date;
}

export type FormResponseAsEmailRepositoryDocument =
  HydratedDocument<FormResponseAsEmailRepository>;

export const FormResponseAsEmailRepositorySchema = SchemaFactory.createForClass(
  FormResponseAsEmailRepository,
);

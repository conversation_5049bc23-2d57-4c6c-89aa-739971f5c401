import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';

export class FormResponseAsEmailDto {
  @IsNotEmpty()
  @IsString()
  form_id: string;

  @IsBoolean()
  @IsNotEmpty()
  sent_to_user: boolean;

  @ValidateIf((o) => o.sent_to_user === false)
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  sent_to_user_or_other: string;

  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  cc_emails: string[];

  @IsOptional()
  @IsNotEmpty()
  status: boolean;
}

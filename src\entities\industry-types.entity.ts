import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { Apps } from './apps.entity';
import { Organization } from './organization.entity';
import { IndustryAppProcess } from './industry-app-process.entity';

@Entity()
export class IndustryTypes {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  industry_type_id: string;

  @Column({ unique: true })
  industry_type: string;

  @Column()
  description: string;

  @OneToMany(
    () => IndustryAppProcess,
    (industry_app_process: IndustryAppProcess) =>
      industry_app_process.industry_types,
  )
  industry_app_process: IndustryAppProcess[];

  @OneToMany(() => Apps, (apps) => apps.industry_types)
  apps: Apps[];

  @OneToMany(() => Organization, (organization) => organization.industry_type)
  organizations: Organization[];

  @Column({ type: Boolean, default: true })
  status: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

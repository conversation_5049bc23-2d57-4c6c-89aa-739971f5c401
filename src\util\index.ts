import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcrypt';
import { CREATED_BY, USER_TYPE } from './interfaces/forms.interface';

export const generateUUID = () => {
  return uuidv4();
};

export const isObjectEmpty = (obj: any) => {
  let isAllEmpty = true;

  for (const key in obj) {
    const value = obj[key];

    if (key === 'client_id') {
      continue;
    }

    if (typeof value === 'object' && value !== null) {
      const isNestedEmpty = isObjectEmpty(value);

      if (!isNestedEmpty) {
        isAllEmpty = false;
        break;
      }
    } else {
      if (value) {
        isAllEmpty = false;
        break;
      }
    }
  }

  // Return true if all fields/sections are empty, otherwise return false
  return isAllEmpty;
};

export const generateAppCode = (name: string) => {
  return `KLEZA_${trimString(name)
    ?.toLowerCase()
    .replace(/\s/g, '')
    .substring(0, 4)
    ?.toUpperCase()}_${randomNumber()}`;
};

export const generateBcryptHash = async (text: string, salt = 12) => {
  try {
    return await bcrypt.hash(text, salt);
  } catch (error) {
    throw error;
  }
};

export const compareBcryptHash = async (
  plainText: string,
  encryptionText: string,
) => {
  try {
    return await bcrypt.compare(plainText, encryptionText);
  } catch (error) {
    console.log(error.message);
    throw error;
  }
};

export const formatStringToKey = (value: string): string =>
  trimString(value)?.toLowerCase().replace(/\s/g, '_');

export const trimString = (value: string): string => {
  if (value) return value.trim();
};

export const getCreatedByFromUser = (user: any): CREATED_BY => {
  let user_type = USER_TYPE.user;
  let user_id = user.user_id;
  if (user.isAdmin) {
    user_type = USER_TYPE.admin;
    user_id = user.admin_id;
  }
  if (user.isOrganization) {
    user_type = USER_TYPE.orgadmin;
    user_id = user.organization_id;
  }
  return {
    user_id,
    user_type,
    timestamp: Date.now(),
  };
};

export const randomNumber = (min = 1111, max = 9999) => {
  return Math.floor(Math.random() * (max - min + 1) + min);
};

export const sanitizeFileName = (fileName: string) => {
  return fileName.replace(/[\\/]/g, '-');
};

export const isEvenNumber = (number) => number % 2 === 0;

export const capitalizeFirstLetter = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

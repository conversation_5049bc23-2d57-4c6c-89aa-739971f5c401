import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  HydratedDocument,
  ObjectId,
  SchemaOptions,
  SchemaTypes,
} from 'mongoose';

const OnBoardOrgChecklistRepoSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'onboard_org_checklist_repository',
  versionKey: false,
};

@Schema(OnBoardOrgChecklistRepoSchemaOptions)
export class OnBoardOrgChecklistRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ unique: true, type: SchemaTypes.String, required: true })
  org_checklist_id: string;

  // @Prop({ type: SchemaTypes.String, required: true })
  // checklist_type: string;

  // @Prop({ type: SchemaTypes.String, required: true })
  // checklist_name: string;

  @Prop({ type: SchemaTypes.String, required: true })
  organization_id: string;

  @Prop({ type: SchemaTypes.Mixed })
  checklist: [CheckListItem];

  @Prop({ default: false, type: SchemaTypes.Boolean })
  status: boolean;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export interface CheckListItem {
  item_id?: string;
  title: string;
  inputs?: Array<CheckListInput>;
}

export interface CheckListInput {
  type: string;
  value?: string;
  label: string;
  required: boolean;
}

export type OnBoardOrgChecklistRepositoryDocument =
  HydratedDocument<OnBoardOrgChecklistRepository>;

export const OnBoardOrgChecklistRepositorySchema = SchemaFactory.createForClass(
  OnBoardOrgChecklistRepository,
);

import { Module } from '@nestjs/common';
import { UserModule } from '../user/user.module';
import { AppsModule } from '../apps/apps.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import IndustryAppProcessController from './industry-app-process.controller';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';

@Module({
  imports: [
    UserModule,
    AppsModule,
    TypeOrmModule.forFeature([IndustryAppProcess, IndustryTypes], 'mysql'),
    MongooseModule.forFeature([
      // { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
    ]),
  ],
  exports: [],
  providers: [IndustryAppProcessService, IndustryTypesService],
  controllers: [IndustryAppProcessController],
})
export class IndustryAppProcessModule {}

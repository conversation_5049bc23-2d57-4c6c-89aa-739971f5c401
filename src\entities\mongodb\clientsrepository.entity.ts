import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import {
  SchemaOptions,
  SchemaTypes,
  ObjectId,
  HydratedDocument,
} from 'mongoose';
import { FormValuesRepository } from './formvaluesrepository.entity';

const clientsRepoSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'clients_repository',
  versionKey: false,
};
@Schema(clientsRepoSchemaOptions)
export class ClientsRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ type: SchemaTypes.String, unique: true, required: true })
  client_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  organization_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  user_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  app_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  name: string;

  @Prop({ type: SchemaTypes.String, required: false })
  mobile_number: string;

  @Prop({ type: SchemaTypes.String, required: false })
  address: string;

  @Prop({ type: SchemaTypes.String, required: false })
  storage_folder_id: string;

  @Prop({ type: SchemaTypes.String, required: false, default: '' })
  storage_folder_name: string;

  @Prop({ type: SchemaTypes.String, required: false })
  storage_location: string;

  @Prop({ type: [{ type: SchemaTypes.ObjectId, ref: 'FormValuesRepository' }] })
  form_values: FormValuesRepository[];

  @Prop({ type: SchemaTypes.Mixed, required: false })
  e_sign_forms: any;

  @Prop({ default: true, type: SchemaTypes.Boolean })
  status: boolean;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type ClientsRepositoryDocument = HydratedDocument<ClientsRepository>;
export const ClientsRepositorySchema =
  SchemaFactory.createForClass(ClientsRepository);

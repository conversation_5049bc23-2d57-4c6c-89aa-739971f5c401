import { HttpException, HttpStatus, Injectable, Param } from '@nestjs/common';
import { Country, State, City } from 'country-state-city';
import axios from 'axios';
import { ConfigurationsService } from './configurations.service';

@Injectable()
export default class CountryStateCityService {
  constructor(private readonly ConfigurationsService: ConfigurationsService) {}

  /**
   * Fetch Address details with latitude and longitude from google
   * @returns Promise<Response>
   */
  async addressDetailsWithLatLong(
    organization_id: string,
    latitude: string,
    longitude: string,
  ): Promise<any> {
    try {
      const organization = await this.ConfigurationsService.getOrganization(
        organization_id,
      );

      if (!organization) {
        throw new Error(
          'Organization details not found! Please send valid organization',
        );
      }

      const configurationDetails = await this.ConfigurationsService.findOne(
        {
          organization,
          type: 'address',
        },
        false,
        null,
        { organization: true },
      );

      if (!configurationDetails) {
        throw new Error(
          'Configuration details not found! Please add in configuration section',
        );
      }

      if (configurationDetails.name == 'google') {
        const response = await axios.get(
          `${configurationDetails.details.hostUrl}/geocode/json`,
          {
            params: {
              latlng: `${latitude},${longitude}`,
              key: configurationDetails.details.apiKey,
            },
          },
        );

        if (response.status) {
          let countryCode: string;
          let state: string;
          const formattedAddress = response.data.results[0].formatted_address;
          const addressComponents = response.data.results[0].address_components;
          for (const component of addressComponents) {
            if (component.types.includes('administrative_area_level_1')) {
              state = component.long_name;
            }

            if (component.types.includes('country')) {
              countryCode = component.short_name;
              break; // Stop searching once we find the country component
            }
          }
          return {
            countryCode,
            state,
          };
        }
      }

      if (configurationDetails.name == 'open_weather') {
        const response = await axios.get(
          `${configurationDetails.details.hostUrl}/geo/1.0/reverse`,
          {
            params: {
              lat: latitude,
              lon: longitude,
              limit: 1,
              appid: configurationDetails.details.apiKey,
            },
          },
        );

        return response.status
          ? {
              countryCode: response?.data[0]?.country || '',
              state: response?.data[0]?.state || '',
            }
          : null;
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Fetch the countries list
   * @returns any
   */
  countriesList(): any {
    try {
      return Country.getAllCountries();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Fetch the states list with countyCode
   * @param countryCode:string
   * @returns any
   */
  statesListByCountry(countryCode: string): any {
    try {
      if (!countryCode) {
        return false;
      }

      return State.getStatesOfCountry(countryCode);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Fetch the cities list with countyCode and stateCode
   * @param countryCode:string
   * @param stateCode:string
   * @returns any
   */
  citiesListByCountry(countryCode: string, stateCode: string): any {
    try {
      if (!countryCode || !stateCode) {
        return false;
      }
      return City.getCitiesOfState(countryCode, stateCode);
    } catch (error) {
      throw error;
    }
  }
}

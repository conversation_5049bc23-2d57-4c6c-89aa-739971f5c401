import {
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  FormValuesRepository,
  FormValuesRepositoryDocument,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import { ClientsRepositoryService } from './clientsrepository.service';
import { OrgFormsRepositoryService } from './orgformsrepository.service';
import { AppsService } from './apps.service';
import * as ExcelJS from 'exceljs';
import { FormsRepository } from 'src/entities/mongodb/formsrepository.entity';
import { Apps } from 'src/entities/apps.entity';
import { OrganizationService } from './organization.service';
// import { ObjectID } from 'typeorm/driver/mongodb/typings';
import EncryptionService from './encryption.service';
import { OnboardingEmployeeService } from './onboarding-employee.service';
import { InjectRepository } from '@nestjs/typeorm';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Repository } from 'typeorm';
import * as cheerio from 'cheerio';

@Injectable()
export class FormValueService {
  constructor(
    @InjectModel(FormValuesRepository.name)
    private readonly FormValueRepository: Model<FormValuesRepositoryDocument>,

    @Inject(forwardRef(() => OrgFormsRepositoryService))
    private readonly orgFormsRepositoryService: OrgFormsRepositoryService,

    private readonly appsService: AppsService,

    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,

    private readonly encryptionService: EncryptionService,

    private readonly clientsRepositoryService: ClientsRepositoryService,

    @InjectRepository(OnboardingEmployee, 'mysql')
    private onboardingEmployeeRepository: Repository<OnboardingEmployee>,
  ) {}

  /**
   * @variable select
   */
  public select: QueryOptions<FormValuesRepository> = {
    _id: false,
    form_value_id: true,
    user_id: true,
    organization_id: true,
    values_encryption_status: true,
    form: true,
    client: true,
    values: true,
    total_points: true,
    scored_points: true,
    completed: true,
    created_at: true,
    updated_at: true,
  };

  /**
   * Get Form Values based on app, organization, client, form
   * @param condition: QueryOptions<FormValuesRepository>
   * @param withDeleted boolean
   * @param select = this.select
   * @param expand string
   * @returns Promise<>
   */
  // async find(
  //   condition: QueryOptions<FormValuesRepository>,
  //   exportToExcel: boolean = false,
  //   withDeleted = false,
  //   select = this.select,
  //   expand = 'form',
  // ): Promise<any> {
  //   try {
  //     if ('id' in condition) {
  //       condition._id = condition.id;
  //       delete condition.id;
  //     }

  //     if (withDeleted) {
  //       condition['deleted_at'] = { $ne: null };
  //     } else {
  //       condition['deleted_at'] = { $eq: null };
  //     }

  //     let query = this.FormValueRepository.find(condition);

  //     if (expand) {
  //       query = query.populate(expand);
  //     }

  //     let formValues: any[] = await query.select(select).catch((error) => {
  //       throw error;
  //     });

  //     formValues = formValues.map((formValue: FormValuesRepository) => {
  //       if (formValue.values_encryption_status) {
  //         formValue.values = this.encryptionService.decryption(
  //           formValue.values,
  //         );
  //       }
  //       return formValue;
  //     });

  //     if (condition?.form && condition?.form?.$in) {
  //       const formOrder = condition.form.$in.map((id) => id.toString());
  //       formValues.sort(
  //         (a, b) =>
  //           formOrder.indexOf(a.form._id.toString()) -
  //           formOrder.indexOf(b.form._id.toString()),
  //       );
  //     }

  //     if (exportToExcel && formValues) {
  //       // const workbook = new ExcelJS.Workbook();

  //       // for (const form of formValues) {
  //       //   if (form.form.name.toLowerCase() === 'adp') {
  //       //     console.log(form.form.fields);
  //       //   }
  //       //   const worksheet = workbook.addWorksheet(form.form.name);

  //       //   //Add Header Row
  //       //   worksheet.addRow(['Section', 'Field Label', 'Field Value']);

  //       //   //Iterate the section
  //       //   for (const sectionKey of Object.keys(form.form.fields)) {
  //       //     const section = form.form.fields[sectionKey];
  //       //     //Iterate the field
  //       //     for (const field of section.fields) {
  //       //       const value = form?.values?.[sectionKey]?.[field?.name];
  //       //       if (field.type === 'signature' && value) {
  //       //         // Convert base64 to image buffer
  //       //         const imageBuffer = Buffer.from(value.split(',')[1], 'base64');

  //       //         // Add a new row for the signature
  //       //         const row = worksheet.addRow([
  //       //           section.group_title,
  //       //           field.label,
  //       //           '',
  //       //         ]);

  //       //         // Add the image to the worksheet
  //       //         const imageId = workbook.addImage({
  //       //           buffer: imageBuffer,
  //       //           extension: 'png',
  //       //         });

  //       //         // Insert the image in the third column of the current row
  //       //         worksheet.addImage(imageId, {
  //       //           tl: { col: 2, row: row.number - 1 },
  //       //           ext: { width: 100, height: 50 }, // Adjust size as needed
  //       //         });
  //       //       } else {
  //       //         // Add Data Row for non-signature fields
  //       //         worksheet.addRow([section.group_title, field.label, value]);
  //       //       }
  //       //     }
  //       //   }
  //       // }

  //       // const buffer = await workbook.xlsx.writeBuffer();
  //       // return buffer;

  //       const workbook = new ExcelJS.Workbook();

  //       function htmlToString(htmlContent: string): string {
  //         if (!htmlContent) return '';
  //         const $ = cheerio.load(htmlContent);
  //         return $.text().trim();
  //       }

  //       function sanitizeSheetName(sheetName: string): string {
  //         return sheetName
  //           .replace(/\//g, '∕')
  //           .replace(/\\/g, '＼')
  //           .replace(/\*/g, '＊')
  //           .replace(/\[/g, '［')
  //           .replace(/\]/g, '］')
  //           .replace(/:/g, '：')
  //           .substring(0, 31);
  //       }

  //       function createFieldWithData(
  //         fields,
  //         value,
  //         startRow,
  //         startCol,
  //         worksheet,
  //       ) {
  //         let currentCol = startCol;
  //         fields.map((field) => {
  //           let fieldRepetition = field.iteration_max_length ?? 1;
  //           worksheet.getColumn(currentCol).width = 25;

  //           let baseLabel =
  //             field.label.trim() !== '' ? field.label : field.input_type;
  //           let fieldValue = value[field.name] || '';
  //           let fieldType = field.input_type;
  //           if (fieldType == 'url') {
  //             fieldValue = field.value;
  //           }
  //           if (field.type == 'editor') {
  //             fieldValue = htmlToString(fieldValue);
  //             // fieldValue = fieldValue;
  //           }

  //           if (fieldRepetition == 1) {
  //             worksheet.getRow(startRow).getCell(currentCol).value = baseLabel;
  //             if (fieldType === 'checkbox') {
  //               worksheet.getRow(startRow + 1).getCell(currentCol).value =
  //                 Array.isArray(fieldValue) ? fieldValue.join(' | ') : '';
  //             } else {
  //               worksheet.getRow(startRow + 1).getCell(currentCol).value =
  //                 fieldValue || '';
  //             }
  //             currentCol++;
  //           } else {
  //             if (
  //               fieldType === 'checkbox' &&
  //               Array.isArray(fieldValue) &&
  //               !Array.isArray(fieldValue[0])
  //             ) {
  //               fieldValue = [fieldValue];
  //             }
  //             for (let i = 0; i < fieldRepetition; i++) {
  //               let cellValue = '';
  //               if (Array.isArray(fieldValue)) {
  //                 if (fieldType !== 'checkbox') {
  //                   cellValue =
  //                     fieldValue[i] !== undefined ? fieldValue[i] : '';
  //                 } else {
  //                   cellValue = Array.isArray(fieldValue[i])
  //                     ? fieldValue[i].join(' | ')
  //                     : '';
  //                 }
  //               } else {
  //                 cellValue = i === 0 ? fieldValue : '';
  //               }
  //               worksheet
  //                 .getRow(startRow)
  //                 .getCell(currentCol).value = `${baseLabel} ${i + 1}`;
  //               worksheet.getRow(startRow + 1).getCell(currentCol).value =
  //                 cellValue;
  //               currentCol++;
  //             }
  //           }
  //         });
  //         return currentCol - startCol;
  //       }

  //       function createFieldWithLabels(fields, startRow, startCol, worksheet) {
  //         let currentCol = startCol;
  //         fields.map((field) => {
  //           worksheet.getColumn(currentCol).width = 25;
  //           worksheet.getRow(startRow).getCell(currentCol).value =
  //             field.label.trim() !== '' ? field.label : field.input_type;
  //           currentCol++;
  //         });
  //         return currentCol - startCol;
  //       }

  //       function createSectionWithData(
  //         section,
  //         value,
  //         startRow,
  //         startCol,
  //         worksheet,
  //       ) {
  //         let sectionRow = startRow,
  //           sectionCol = startCol;

  //         const isEmpty =
  //           value == null ||
  //           (typeof value === 'object' && Object.keys(value).length === 0);

  //         const requiredFields = section.fields.filter(
  //           (field) =>
  //             !['signature', 'image', 'paragraph', 'download'].includes(
  //               field.type,
  //             ) && field.input_type !== 'file',
  //         );

  //         if (isEmpty) {
  //           return createFieldWithLabels(
  //             requiredFields,
  //             sectionRow,
  //             sectionCol,
  //             worksheet,
  //           );
  //         } else {
  //           return createFieldWithData(
  //             requiredFields,
  //             value,
  //             sectionRow,
  //             sectionCol,
  //             worksheet,
  //           );
  //         }
  //       }

  //       for (const form of formValues) {
  //         var startRow = 2,
  //           startCol = 1;
  //         const formName = sanitizeSheetName(form.form.name);
  //         const worksheet = workbook.addWorksheet(formName);
  //         const sections = form.form.fields;
  //         const values = form.values || {};
  //         for (const sectionKey in sections) {
  //           const section = sections[sectionKey];
  //           const isIterative = section?.is_iterative_or_not ?? false;
  //           if (isIterative == false) {
  //             const sectionValue = values[sectionKey] || {};
  //             let sectionLength =
  //               createSectionWithData(
  //                 section,
  //                 sectionValue,
  //                 startRow,
  //                 startCol,
  //                 worksheet,
  //               ) || 0;
  //             if (sectionLength > 0) {
  //               worksheet.mergeCells(
  //                 startRow - 1,
  //                 startCol,
  //                 startRow - 1,
  //                 startCol + sectionLength - 1,
  //               );
  //               const cell = worksheet.getRow(startRow - 1).getCell(startCol);
  //               cell.value = section.group_title;
  //               cell.alignment = { horizontal: 'center', vertical: 'middle' };
  //               startCol = startCol + sectionLength;
  //             }
  //           } else if (section.iteration_max_length > 1) {
  //             const value = values[sectionKey];
  //             if (Array.isArray(value)) {
  //               for (let i = 0; i < section.iteration_max_length; i++) {
  //                 let sectionValue = value[i] || {};
  //                 let sectionLength =
  //                   createSectionWithData(
  //                     section,
  //                     sectionValue,
  //                     startRow,
  //                     startCol,
  //                     worksheet,
  //                   ) || 0;
  //                 if (sectionLength > 0) {
  //                   worksheet.mergeCells(
  //                     startRow - 1,
  //                     startCol,
  //                     startRow - 1,
  //                     startCol + sectionLength - 1,
  //                   );
  //                   const cell = worksheet
  //                     .getRow(startRow - 1)
  //                     .getCell(startCol);
  //                   cell.value = section.group_title + '-' + (i + 1);
  //                   cell.alignment = {
  //                     horizontal: 'center',
  //                     vertical: 'middle',
  //                   };
  //                   startCol = startCol + sectionLength;
  //                 }
  //               }
  //             } else {
  //               for (let i = 0; i < section.iteration_max_length; i++) {
  //                 let sectionValue = i === 0 ? value : {};
  //                 let sectionLength =
  //                   createSectionWithData(
  //                     section,
  //                     sectionValue,
  //                     startRow,
  //                     startCol,
  //                     worksheet,
  //                   ) || 0;
  //                 if (sectionLength > 0) {
  //                   worksheet.mergeCells(
  //                     startRow - 1,
  //                     startCol,
  //                     startRow - 1,
  //                     startCol + sectionLength - 1,
  //                   );
  //                   const cell = worksheet
  //                     .getRow(startRow - 1)
  //                     .getCell(startCol);
  //                   cell.value = section.group_title + '-' + (i + 1);
  //                   cell.alignment = {
  //                     horizontal: 'center',
  //                     vertical: 'middle',
  //                   };
  //                   startCol = startCol + sectionLength;
  //                 }
  //               }
  //             }
  //           }
  //         }
  //       }

  //       const buffer = await workbook.xlsx.writeBuffer();
  //       return buffer;
  //     }

  //     return formValues;
  //   } catch (error) {
  //     console.log(error);
  //     throw error;
  //   }
  // }

  // async find(
  //   condition: QueryOptions<FormValuesRepository>,
  //   exportToExcel: boolean = false,
  //   withDeleted = false,
  //   select = this.select,
  //   expand = 'form',
  // ): Promise<any> {
  //   try {
  //     if ('id' in condition) {
  //       condition._id = condition.id;
  //       delete condition.id;
  //     }

  //     if (withDeleted) {
  //       condition['deleted_at'] = { $ne: null };
  //     } else {
  //       condition['deleted_at'] = { $eq: null };
  //     }

  //     let query = this.FormValueRepository.find(condition);

  //     if (expand) {
  //       query = query.populate(expand);
  //     }

  //     let formValues: any[] = await query.select(select).catch((error) => {
  //       throw error;
  //     });

  //     formValues = formValues.map((formValue: FormValuesRepository) => {
  //       if (formValue.values_encryption_status) {
  //         formValue.values = this.encryptionService.decryption(
  //           formValue.values,
  //         );
  //       }
  //       return formValue;
  //     });

  //     if (condition?.form && condition?.form?.$in) {
  //       const formOrder = condition.form.$in.map((id) => id.toString());
  //       formValues.sort(
  //         (a, b) =>
  //           formOrder.indexOf(a.form._id.toString()) -
  //           formOrder.indexOf(b.form._id.toString()),
  //       );
  //     }

  //     if (exportToExcel && formValues) {
  //       const workbook = new ExcelJS.Workbook();

  //       function htmlToString(htmlContent: string): string {
  //         if (!htmlContent) return '';
  //         const $ = cheerio.load(htmlContent);
  //         return $.text().trim();
  //       }

  //       //TODO Need re-factor this clients logic
  //       const assessmentClients = await this.clientsRepositoryService.findAll();

  //       function sanitizeSheetName(sheetName: string): string {
  //         return sheetName
  //           .replace(/\//g, '∕')
  //           .replace(/\\/g, '＼')
  //           .replace(/\*/g, '＊')
  //           .replace(/\[/g, '［')
  //           .replace(/\]/g, '］')
  //           .replace(/:/g, '：')
  //           .substring(0, 31);
  //       }

  //       function createFieldWithData(
  //         fields,
  //         value,
  //         startRow,
  //         startCol,
  //         worksheet,
  //       ) {
  //         let currentCol = startCol;
  //         fields.forEach((field) => {
  //           worksheet.getColumn(currentCol).width = 25;

  //           let baseLabel =
  //             field.label.trim() !== '' ? field.label : field.input_type;
  //           let fieldValue = value[field.name] || '';
  //           let fieldType = field.input_type;

  //           if (fieldType === 'url') {
  //             fieldValue = field.value;
  //           }
  //           if (field.type === 'editor') {
  //             fieldValue = htmlToString(fieldValue);
  //           }

  //           worksheet.getRow(startRow).getCell(currentCol).value = baseLabel;
  //           if (fieldType === 'checkbox') {
  //             worksheet.getRow(startRow + 1).getCell(currentCol).value =
  //               Array.isArray(fieldValue)
  //                 ? fieldValue.join(' | ')
  //                 : fieldValue || '';
  //           } else {
  //             worksheet.getRow(startRow + 1).getCell(currentCol).value =
  //               fieldValue || '';
  //           }
  //           currentCol++;
  //         });
  //         return currentCol - startCol;
  //       }

  //       function createFieldWithLabels(fields, startRow, startCol, worksheet) {
  //         let currentCol = startCol;
  //         fields.forEach((field) => {
  //           worksheet.getColumn(currentCol).width = 25;
  //           worksheet.getRow(startRow).getCell(currentCol).value =
  //             field.label.trim() !== '' ? field.label : field.input_type;
  //           currentCol++;
  //         });
  //         return currentCol - startCol;
  //       }

  //       function createSectionWithData(
  //         section,
  //         value,
  //         startRow,
  //         startCol,
  //         worksheet,
  //       ) {
  //         let sectionRow = startRow,
  //           sectionCol = startCol;

  //         const isEmpty =
  //           value == null ||
  //           (typeof value === 'object' && Object.keys(value).length === 0);

  //         const requiredFields = section.fields.filter(
  //           (field) =>
  //             !['signature', 'image', 'paragraph', 'download'].includes(
  //               field.type,
  //             ) && field.input_type !== 'file',
  //         );

  //         if (isEmpty) {
  //           return createFieldWithLabels(
  //             requiredFields,
  //             sectionRow,
  //             sectionCol,
  //             worksheet,
  //           );
  //         } else {
  //           return createFieldWithData(
  //             requiredFields,
  //             value,
  //             sectionRow,
  //             sectionCol,
  //             worksheet,
  //           );
  //         }
  //       }

  //       formValues = this.mergeFormValues(formValues);
  //       for (const form of formValues) {
  //         let startRow = 2,
  //           startCol = 1;
  //         const formName = sanitizeSheetName(form.form.name);
  //         const worksheet = workbook.addWorksheet(formName);
  //         const sections = form.form.fields;

  //         // Write headers (section titles and field labels)
  //         for (const sectionKey in sections) {
  //           const section = sections[sectionKey];
  //           const sectionValue = Array.isArray(form.values)
  //             ? form.values[0]?.[sectionKey] || {}
  //             : form.values[sectionKey] || {};
  //           let sectionLength =
  //             createSectionWithData(
  //               section,
  //               sectionValue,
  //               startRow,
  //               startCol,
  //               worksheet,
  //             ) || 0;
  //           if (sectionLength > 0) {
  //             worksheet.mergeCells(
  //               startRow - 1,
  //               startCol,
  //               startRow - 1,
  //               startCol + sectionLength - 1,
  //             );
  //             const cell = worksheet.getRow(startRow - 1).getCell(startCol);
  //             cell.value = section.group_title;
  //             cell.alignment = { horizontal: 'center', vertical: 'middle' };
  //             startCol = startCol + sectionLength;
  //           }
  //         }

  //         // Write multiple response rows
  //         const valuesArray = Array.isArray(form.values)
  //           ? form.values
  //           : [form.values];

  //           valuesArray.forEach((values, responseIndex) => {
  //           let currentCol = 1;
  //           for (const sectionKey in sections) {
  //             const section = sections[sectionKey];
  //             const sectionValue = values[sectionKey] || {};
  //             const requiredFields = section.fields.filter(
  //               (field) =>
  //                 !['signature', 'image', 'paragraph', 'download'].includes(
  //                   field.type,
  //                 ) && field.input_type !== 'file',
  //             );

  //             requiredFields.forEach((field) => {
  //               let fieldValue = sectionValue[field.name] || '';
  //               let fieldType = field.input_type;

  //               if (fieldType === 'url') {
  //                 fieldValue = field.value;
  //               }
  //               if (field.type === 'editor') {
  //                 fieldValue = htmlToString(fieldValue);
  //               }

  //               if (
  //                 field.input_type === 'dropdown' &&
  //                 field.type === 'clients_list'
  //               ) {
  //                 const client = Array.isArray(assessmentClients)
  //                   ? assessmentClients.find(
  //                       (c: any) => c.client_id === fieldValue,
  //                     )
  //                   : null;
  //                 fieldValue = client
  //                   ? `${client?.name}(${client?.mobile_number})`
  //                   : '';
  //               }

  //               if (fieldType === 'checkbox') {
  //                 worksheet
  //                   .getRow(startRow + responseIndex + 1)
  //                   .getCell(currentCol).value = Array.isArray(fieldValue)
  //                   ? fieldValue.join(' | ')
  //                   : fieldValue || '';
  //               } else {
  //                 worksheet
  //                   .getRow(startRow + responseIndex + 1)
  //                   .getCell(currentCol).value = fieldValue || '';
  //               }

  //               currentCol++;
  //             });
  //           }
  //         });

  //         startRow += valuesArray.length; // Adjust startRow for next form, if needed
  //       }

  //       const buffer = await workbook.xlsx.writeBuffer();
  //       return buffer;
  //     }

  //     return formValues;
  //   } catch (error) {
  //     console.log(error);
  //     throw error;
  //   }
  // }

  async find(
    condition: QueryOptions<FormValuesRepository>,
    exportToExcel: boolean = false,
    withDeleted = false,
    select = this.select,
    expand = 'form',
  ): Promise<any> {
    try {
      // Existing query setup logic remains unchanged
      if ('id' in condition) {
        condition._id = condition.id;
        delete condition.id;
      }
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }

      let query = this.FormValueRepository.find(condition);
      if (expand) {
        query = query.populate(expand);
      }

      let formValues: any[] = await query.select(select).catch((error) => {
        throw error;
      });

      formValues = formValues.map((formValue: FormValuesRepository) => {
        if (formValue.values_encryption_status) {
          formValue.values = this.encryptionService.decryption(
            formValue.values,
          );
        }
        return formValue;
      });

      if (condition?.form && condition?.form?.$in) {
        const formOrder = condition.form.$in.map((id) => id.toString());
        formValues.sort(
          (a, b) =>
            formOrder.indexOf(a.form._id.toString()) -
            formOrder.indexOf(b.form._id.toString()),
        );
      }

      if (exportToExcel && formValues) {
        const workbook = new ExcelJS.Workbook();

        //TODO Need re-factor this clients logic
        const assessmentClients = await this.clientsRepositoryService.findAll();

        function htmlToString(htmlContent: string): string {
          if (!htmlContent) return '';
          const $ = cheerio.load(htmlContent);
          return $.text().trim();
        }

        function sanitizeSheetName(sheetName: string): string {
          return sheetName
            .replace(/\//g, '∕')
            .replace(/\\/g, '＼')
            .replace(/\*/g, '＊')
            .replace(/\[/g, '［')
            .replace(/\]/g, '］')
            .replace(/:/g, '：')
            .substring(0, 31);
        }

        // Generate field headers, iterating only if is_iterative_or_not is true
        function createFieldHeaders(fields, startCol, worksheet) {
          let currentCol = startCol;
          fields.forEach((field) => {
            const baseLabel =
              field.label.trim() !== '' ? field.label : field.input_type;
            const isIterative = field.is_iterative_or_not === true; // Explicitly check for true
            const fieldRepetition = isIterative
              ? field.iteration_max_length ?? 1
              : 1;
            for (let i = 0; i < fieldRepetition; i++) {
              const label =
                fieldRepetition > 1 ? `${baseLabel} ${i + 1}` : baseLabel;
              worksheet.getColumn(currentCol).width = 25;
              worksheet.getRow(2).getCell(currentCol).value = label;
              currentCol++;
            }
          });
          return currentCol - startCol;
        }

        // Generate section headers, accounting for section iterations
        function createSectionHeaders(section, startCol, worksheet) {
          let currentCol = startCol;
          const isIterative = section?.is_iterative_or_not ?? false;
          const iterationMax = isIterative ? section.iteration_max_length : 1;
          for (let iter = 0; iter < iterationMax; iter++) {
            const sectionTitle = isIterative
              ? `${section.group_title}-${iter + 1}`
              : section.group_title;
            const requiredFields = section.fields.filter(
              (field) =>
                !['signature', 'image', 'paragraph', 'download'].includes(
                  field.type,
                ) && field.input_type !== 'file',
            );
            const sectionLength = createFieldHeaders(
              requiredFields,
              currentCol,
              worksheet,
            );
            if (sectionLength > 0) {
              worksheet.mergeCells(
                1,
                currentCol,
                1,
                currentCol + sectionLength - 1,
              );
              worksheet.getRow(1).getCell(currentCol).value = sectionTitle;
              worksheet.getRow(1).getCell(currentCol).alignment = {
                horizontal: 'center',
                vertical: 'middle',
              };
              currentCol += sectionLength;
            }
          }
          return currentCol - startCol;
        }

        // Write field values, iterating only if is_iterative_or_not is true
        function writeFieldValues(
          fields,
          value,
          startCol,
          worksheet,
          row,
          assessmentClients,
        ) {
          let currentCol = startCol;
          fields.forEach((field) => {
            const isIterative = field.is_iterative_or_not === true; // Explicitly check for true
            const fieldRepetition = isIterative
              ? field.iteration_max_length ?? 1
              : 1;
            let fieldValue =
              value[field.name] || (fieldRepetition > 1 ? [] : '');
            if (field.input_type === 'url') {
              fieldValue = field.value;
            }
            if (field.type === 'editor') {
              fieldValue = htmlToString(fieldValue);
            }
            if (
              field.input_type === 'dropdown' &&
              field.type === 'clients_list'
            ) {
              const client = Array.isArray(assessmentClients)
                ? assessmentClients.find((c: any) => c.client_id === fieldValue)
                : null;
              fieldValue = client
                ? `${client.name}(${client.mobile_number})`
                : '';
            }
            if (fieldRepetition > 1) {
              if (!Array.isArray(fieldValue)) fieldValue = [fieldValue];
              for (let i = 0; i < fieldRepetition; i++) {
                let cellValue = fieldValue[i] || '';
                if (
                  field.input_type === 'checkbox' &&
                  Array.isArray(cellValue)
                ) {
                  cellValue = cellValue.join(' | ');
                }
                worksheet.getRow(row).getCell(currentCol).value = cellValue;
                currentCol++;
              }
            } else {
              if (
                field.input_type === 'checkbox' &&
                Array.isArray(fieldValue)
              ) {
                fieldValue = fieldValue.join(' | ');
              }
              worksheet.getRow(row).getCell(currentCol).value =
                fieldValue || '';
              currentCol++;
            }
          });
          return currentCol - startCol;
        }

        // Write section values, handling section iterations
        function writeSectionValues(
          section,
          sectionValue,
          startCol,
          worksheet,
          row,
          assessmentClients,
        ) {
          let currentCol = startCol;
          const isIterative = section?.is_iterative_or_not ?? false;
          const iterationMax = isIterative ? section.iteration_max_length : 1;
          const sectionValues = isIterative
            ? Array.isArray(sectionValue)
              ? sectionValue
              : []
            : [sectionValue];
          for (let iter = 0; iter < iterationMax; iter++) {
            const iterValue = sectionValues[iter] || {};
            const requiredFields = section.fields.filter(
              (field) =>
                !['signature', 'image', 'paragraph', 'download'].includes(
                  field.type,
                ) && field.input_type !== 'file',
            );
            currentCol += writeFieldValues(
              requiredFields,
              iterValue,
              currentCol,
              worksheet,
              row,
              assessmentClients,
            );
          }
          return currentCol - startCol;
        }

        formValues = this.mergeFormValues(formValues);
        for (const form of formValues) {
          let startRow = 3; // Start responses from row 3
          const formName = sanitizeSheetName(form.form.name);
          const worksheet = workbook.addWorksheet(formName);
          const sections = form.form.fields;

          // Write headers
          let currentCol = 1;
          for (const sectionKey in sections) {
            const section = sections[sectionKey];
            currentCol += createSectionHeaders(section, currentCol, worksheet);
          }

          // Write response values
          const valuesArray = Array.isArray(form.values)
            ? form.values
            : [form.values];
          valuesArray.forEach((values, responseIndex) => {
            let col = 1;
            for (const sectionKey in sections) {
              const section = sections[sectionKey];
              const sectionValue =
                values[sectionKey] || (section.is_iterative_or_not ? [] : {});
              col += writeSectionValues(
                section,
                sectionValue,
                col,
                worksheet,
                startRow + responseIndex,
                assessmentClients,
              );
            }
          });
        }

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
      }

      return formValues;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  mergeFormValues = (input: any[]) => {
    const result: any[] = [];
    const formMap = new Map();

    for (const item of input) {
      const formId = item.form.form_id;
      const hasMultiple = item.form.has_multiple_form_responses;

      if (hasMultiple) {
        if (!formMap.has(formId)) {
          formMap.set(formId, {
            form: item.form,
            values: [item.values],
          });
          result.push(formMap.get(formId));
        } else {
          formMap.get(formId).values.push(item.values);
        }
      } else {
        result.push(item);
      }
    }

    return result;
  };

  async getDocumentsInfo(
    organization_id: string,
    onboarding_employee_id?: string,
  ) {
    try {
      const onboarding_employee: any =
        await this.onboardingEmployeeRepository.findOne({
          where: { onboarding_employee_id },
        });

      if (!onboarding_employee) {
        throw new UnauthorizedException('Onboarding Employee not found');
      }

      const organization = await this.organizationService.findOne(
        { organization_id },
        false,
        { id: true },
      );

      if (!organization) {
        throw new UnauthorizedException('Organization not found');
      }

      let getApps: any = await this.appsService.list(
        {
          organizations: organization,
        },
        null,
        null,
        { industry_app_process: true },
      );

      const onboardingApps = ['HC_OBRD'];

      getApps = getApps.filter((app: Apps) =>
        onboardingApps.includes(app?.industry_app_process?.process_code),
      );

      const app: Apps = getApps ? getApps[0] : null;

      if (!app) {
        return [];
      }

      const organizationForms = await this.orgFormsRepositoryService.find(
        {
          app_id: app.app_id,
          organization: organization_id,
          status: true,
        },
        false,
        { id: true, fields: true },
      );

      if (!organizationForms?.length) {
        return [];
      }

      let documentForms = [];

      let totalFileFields = 0;

      organizationForms.map((form: any) => {
        let documentForm: any = { id: form._id, fields: {} };

        form?.fields &&
          Object.entries(form.fields).forEach(
            ([sectionName, sectionDetails]: any) => {
              let documentFields = [];
              sectionDetails?.fields?.map((field) => {
                if (field.input_type == 'file') {
                  documentFields.push({ name: field.name, label: field.label });
                  totalFileFields++;
                }
              });
              if (documentFields.length) {
                if (!documentForm.fields[sectionName]) {
                  documentForm.fields[sectionName] = [];
                }
                documentForm.fields[sectionName] = documentFields;
              }
            },
          );

        if (Object.keys(documentForm.fields).length > 0) {
          documentForms.push(documentForm);
        }
      });

      let documents = [];

      if (documentForms.length) {
        await Promise.all(
          documentForms?.map(async (orgForm: any) => {
            let formValues = await this.FormValueRepository.find({
              form: orgForm.id,
              user_id: onboarding_employee_id,
            });

            formValues?.map((formValue: any) => {
              Object.entries(orgForm.fields).forEach(
                ([sectionName, sectionDetails]: any) => {
                  sectionDetails?.map((field: any) => {
                    const formResponse = formValue?.['values_encryption_status']
                      ? this.encryptionService.decryption(formValue.values)
                      : formValue?.values;

                    if (formResponse?.[sectionName]?.[field.name]) {
                      documents.push({
                        value: formResponse?.[sectionName]?.[field.name],
                        name: field.label,
                      });
                    }
                  });
                },
              );
            });
          }),
        );
      }

      if (onboarding_employee?.bg_verification_document) {
        documents.push({
          value: onboarding_employee?.bg_verification_document,
          name: 'Background Verification Document',
        });
      }

      return documents;
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

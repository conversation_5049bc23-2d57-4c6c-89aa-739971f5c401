import {
  Controller,
  Get,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
  UseGuards,
  Put,
  HttpException,
  HttpStatus,
  Req,
  Param,
  Res,
  Query,
  NotFoundException,
  Delete,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';

import { UserAuth } from 'src/guards/user.guard';
import { UserService } from 'src/services/user.service';
import { UserDto, UserRegisterDto } from 'src/dto/user.dto';
import {
  InjectUserToBody,
  InjectUserToParam,
} from 'src/util/interceptor/user.interceptor';
import { AdminOrOrganizationAuth } from 'src/guards/adminOrOrganization.guard';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import {
  ESignRequestLogsRepository,
  ESignRequestLogsRepositoryDocument,
} from 'src/entities/mongodb/e-sign-request-log.entity';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { FIELD, GROUP } from 'src/util/interfaces/forms.interface';
import * as moment from 'moment';
import { Apps } from 'src/entities/apps.entity';
import { AppsService } from 'src/services/apps.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';

@Controller('user')
export default class UserController {
  constructor(
    private userService: UserService,

    private onboardingEmployeeService: OnboardingEmployeeService,

    private orgFormsService: OrgFormsRepositoryService,

    private clientsRepositoryService: ClientsRepositoryService,

    private appsService: AppsService,

    @InjectModel(ESignRequestLogsRepository.name)
    private eSignRequestLogsRepository: Model<ESignRequestLogsRepositoryDocument>,
  ) { }

  @Post('/')
  @InjectUserToBody()
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async registerUser(
    @Req() request: Request,
    @Body() body: UserRegisterDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      if (request.user['isOrganization']) {
        body.organization = request.user['organization_id'];
      } else {
        if (!body.organization) {
          throw {
            status: HttpStatus.BAD_REQUEST,
            message: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: ['organization should not be empty'],
              error: 'Bad Request',
            },
          };
        }
      }
      // const data = body;
      const data = await this.userService.create(body);
      return data
        ? response.send({
          status: true,
          message: 'User Created Successfully',
        })
        : response.status(HttpStatus.CONFLICT).send({
          status: false,
          message: 'Unable to create the user',
        });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getList(
    @Req() request: Request,
    @Res() response: Response,
    @Query('organization_id') organization_id?: string,
    @Query('pagination') pagination?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<Response> {
    try {
      if (!organization_id) {
        organization_id = request.user['isOrganization']
          ? request.user['organization_id']
          : '';
      }

      return response.json({
        status: true,
        message: 'User list fetched successfully',
        data: await this.userService.list(
          organization_id,
          pagination,
          page,
          limit,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/forms')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async getUserForms(
    @Req() request: Request,
    @Res() response: Response,
    @Query('client_id') client_id?: string,
    @Query('from') from?: string,
    @Query('is_quiz_form') is_quiz_form?: boolean,
  ): Promise<Response> {
    try {
      const user = request.user;
      /* const forms = await this.orgFormsService.find(
        {
          organization: user['organization'],
          app_id: user['app_id'],
          status: true,
          is_sub_form: false,
        },
        false,
      ); */

      const isClientRequired =
        await this.clientsRepositoryService.checkClientRequiredOrNot(
          request.headers['kleza-app-code'],
        );

      const forms = await this.orgFormsService.getUserForms(
        user,
        isClientRequired,
        client_id,
        is_quiz_form,
        from,
      );

      if (forms.length) return response.send({ status: true, data: forms });

      return response
        .status(HttpStatus.NOT_FOUND)
        .send({ status: false, message: 'Forms Not Found' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/v2/forms')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async getV2UserForms(
    @Req() request: Request,
    @Res() response: Response,
    @Query('primary_form_id') primary_form_id?: string,
    @Query('is_quiz_form') is_quiz_form?: boolean,
  ): Promise<Response> {
    try {
      const user = request.user;

      const isClientRequired =
        await this.clientsRepositoryService.checkClientRequiredOrNot(
          request.headers['kleza-app-code'],
        );

      if (isClientRequired && !primary_form_id) {
        throw new Error('primary_form_id is required');
      }

      return response.status(200).send({
        status: true,
        message: 'Forms list fetched successfully',
        data: await this.orgFormsService.getUserFormsV2(
          user,
          isClientRequired,
          primary_form_id,
          is_quiz_form,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/forms/:form_id/sub-forms')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async getSubForms(
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Res() response: Response,
    @Query('client_id') client_id?: string,
  ): Promise<Response> {
    try {
      const user = request.user;

      const isClientRequired =
        await this.clientsRepositoryService.checkClientRequiredOrNot(
          request.headers['kleza-app-code'],
        );

      return response.status(HttpStatus.OK).send({
        status: true,
        message: 'Forms list fetched successfully',
        data: await this.orgFormsService.getSubForms(
          user,
          form_id,
          isClientRequired,
          client_id,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/forms/:form_id')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async getForm(
    @Req() request: Request,
    @Res() response: Response,
    @Param('form_id') form_id: string,
    @Query('client_id') client_id?: string,
  ): Promise<Response> {
    try {
      const user = request.user;

      let isClientRequired: boolean;

      if (request.headers['kleza-app-code']) {
        isClientRequired =
          await this.clientsRepositoryService.checkClientRequiredOrNot(
            request.headers['kleza-app-code'],
          );
      } else {
        throw new BadRequestException('App code required from headers');
      }

      const data = await this.orgFormsService.getFormValueByIdAndUserId(
        user['organization'],
        user['app_id'],
        form_id,
        client_id,
        user['user_id'],
        isClientRequired,
      );
      return data
        ? response.status(200).send({ status: true, data })
        : response
          .status(HttpStatus.NOT_FOUND)
          .send({ status: false, message: 'Form Not Found.' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/v2/forms/:form_id')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async getV2Form(
    @Req() request: Request,
    @Res() response: Response,
    @Param('form_id') form_id: string,
    @Query('primary_form_id') primary_form_id?: string,
  ): Promise<Response> {
    try {
      const user = request.user;

      let isClientRequired: boolean;

      if (request.headers['kleza-app-code']) {
        isClientRequired =
          await this.clientsRepositoryService.checkClientRequiredOrNot(
            request.headers['kleza-app-code'],
          );
      } else {
        throw new BadRequestException('App code required from headers');
      }

      if (isClientRequired && !primary_form_id) {
        throw new BadRequestException('primary_form_id is required');
      }

      return response.status(200).send({
        status: true,
        message: 'Form details fetched successfully',
        data: await this.orgFormsService.getFormValueByIdAndUserIdV2(
          user['organization'],
          user['app_id'],
          form_id,
          primary_form_id,
          user['user_id'],
          isClientRequired,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/forms/e-sign/:form_id')
  async getFormESign(
    @Res() response: Response,
    @Param('form_id') form_id: string,
    @Query('request_id') request_id?: string,
  ): Promise<Response> {
    try {
      const e_sign_request_log = await this.eSignRequestLogsRepository.findOne({
        e_sign_request_logs_id: request_id,
      });

      if (!e_sign_request_log) {
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Invalid Request Id',
        };
      }

      if (moment().unix() > e_sign_request_log.expiry_timestamp) {
        throw {
          status: HttpStatus.REQUEST_TIMEOUT,
          message: 'E-Sign Request link expired',
        };
      }

      const data = await this.orgFormsService.findOne({ form_id });

      const sections = data.fields;

      const sectionsKey = Object.keys(sections);

      const fieldsObj = {};

      const groups = sectionsKey.map((key: string): GROUP => {
        const fields = data.fields[key].fields.sort((f1: FIELD, f2: FIELD) =>
          f1.field_index > f2.field_index ? 1 : -1,
        );
        fieldsObj[key] = fields.map((field: FIELD) => ({
          name: field.name,
          is_iterative_or_not: field.is_iterative_or_not,
        }));

        return {
          ...data.fields[key],
          fields: fields,
          group_key: key,
          group_description: data?.fields[key]?.group_description || '',
        };
      });

      return data
        ? response.status(200).send({
          status: true,
          data: {
            form_id: data.form_id,
            name: data.name,
            description: data.description,
            groups,
            fields: fieldsObj,
          },
        })
        : response
          .status(HttpStatus.NOT_FOUND)
          .send({ status: false, message: 'Form Not Found.' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Save form value pi
   * @param body any
   * @param form_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('forms/:form_id')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  @UsePipes(ValidationPipe)
  async saveFormValues(
    @Body() body: any,
    @Req() request: Request,
    @Param('form_id') form_id: string,
    @Query('validation') validation: string = 'true',
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let data: any;
      let isClientRequired: boolean;
      const user = request.user;

      //check client creation required or not
      if (request.headers['kleza-app-code']) {
        isClientRequired =
          await this.clientsRepositoryService.checkClientRequiredOrNot(
            request.headers['kleza-app-code'],
          );
      } else {
        throw new BadRequestException('App code required from headers');
      }

      if (validation && validation == 'true') {
        data = await this.orgFormsService.saveData(
          form_id,
          body,
          user,
          isClientRequired,
        );
      } else {
        data = await this.orgFormsService.saveDataWithOutValidation(
          form_id,
          body,
          user,
          isClientRequired,
        );
      }

      return typeof data === 'boolean'
        ? response.status(200).send({
          status: true,
          message: 'Form data have been saved successfully',
        })
        : response
          .status(HttpStatus.BAD_REQUEST)
          .send({ status: false, message: 'Bad Request.', data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Save form value pi
   * @param body any
   * @param form_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('v2/forms/:form_id')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  @UsePipes(ValidationPipe)
  async saveFormResponse(
    @Body() body: any,
    @Req() request: Request,
    @Param('form_id') form_id: string,
    @Query('validation') validation: boolean = true,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let isClientRequired: boolean;

      const user = request.user;

      //check client creation required or not
      if (request.headers['kleza-app-code']) {
        isClientRequired =
          await this.clientsRepositoryService.checkClientRequiredOrNot(
            request.headers['kleza-app-code'],
          );
      } else {
        throw new BadRequestException('App code required from headers');
      }

      return response.status(200).send({
        status: true,
        message: 'Form data have been saved successfully.',
        data: await this.orgFormsService.saveFormResponse(
          form_id,
          body,
          user,
          isClientRequired,
          validation,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Save form value pi
   * @param body any
   * @param form_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('forms/e-sign/:form_id')
  @UsePipes(ValidationPipe)
  async saveFormValuesWithPublic(
    @Body() body: any,
    @Param('form_id') form_id: string,
    @Query('request_id') request_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const e_sign_request_log = await this.eSignRequestLogsRepository.findOne({
        e_sign_request_logs_id: request_id,
      });

      if (!e_sign_request_log) {
        return response.status(HttpStatus.BAD_REQUEST).send({
          status: false,
          message: 'Bad Request.',
          data: 'Invalid request id.',
        });
      }

      body.client_id = e_sign_request_log.client_id;

      const user = await this.userService.findOne({
        user_id: e_sign_request_log.user_id,
      });

      return response.status(200).send({
        status: true,
        message: 'Form data have been saved successfully.',
        data: await this.orgFormsService.saveDataWithOutValidation(
          form_id,
          body,
          user,
          true,
          e_sign_request_log,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:id')
  @UseGuards(AuthGuard('jwt'))
  getOne(@Param('id') id?: string, @Query('relation') relation?: string) {
    try {
      let relations = [];
      const relationObj = {};
      if (relation) {
        relations = relation.split(',');
        relations.forEach((relation) => {
          relationObj[relation] = true;
        });
      }

      const user = this.userService.findOne(
        { user_id: id },
        false,
        { ...this.userService.select, id: true },
        relationObj,
      );

      if (!user) throw new NotFoundException(`User Not Found`);
      return user;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/:id?')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(ValidationPipe)
  async update(
    @Body() body: UserDto,
    @Req() req: Request,
    @Res() res: Response,
    @Param('id') id?: string,
  ) {
    try {
      let userid = id;
      if (req.user['isUser']) userid = req.user['user_id'];

      body.organization = req.user['isOrganization']
        ? req.user['organization_id']
        : body.organization;

      if (!userid)
        throw new HttpException(
          'Please check the accessed url. Must pass the user id in url',
          HttpStatus.BAD_REQUEST,
        );

      return res.json({
        status: true,
        message: 'User details updated successfully',
        data: await this.userService.update(body, userid),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('/de-activate')
  @UseGuards(AuthGuard('jwt'))
  async deActivateEmployeeApplicant(@Req() req, @Res() res) {
    try {
      const app: Apps = await this.appsService.findOne(
        { app_code: req.headers['kleza-app-code'] },
        false,
        { id: true },
        { industry_app_process: true },
      );

      if (!app) {
        throw new HttpException('Invalid App', HttpStatus.BAD_REQUEST);
      }

      const onboardingApps = ['HC_OBRD'];

      return res.send({
        status: true,
        message: 'Account deleted successfully',
        data: onboardingApps.includes(app?.industry_app_process?.process_code)
          ? await this.onboardingEmployeeService.delete(
            req?.user['onboarding_employee_id'],
          )
          : await this.userService.delete(req?.user['user_id']),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('/:id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async delete(@Param('id') id: string, @Req() req, @Res() res) {
    try {
      const organization_id = req.user['isOrganization']
        ? req.user['organization_id']
        : '';

      await this.userService.delete(id, organization_id);

      return res.send({
        status: true,
        message: 'User has been deleted successfully',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

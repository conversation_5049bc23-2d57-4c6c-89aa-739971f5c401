import { FormsRepository } from 'src/entities/mongodb/formsrepository.entity';
import { OrgFormsRepository } from 'src/entities/mongodb/orgformsrepository.entity';

export interface ATTRIBUTES {
  attr_type: string;
  attr_value: string;
}
export interface OPTIONS {
  value: string;
  label: string;
}

export interface INPUT_FIELD {
  input_type: string;
}

export interface OPTION_FIELD {
  options: OPTIONS[];
}

export interface FIELD extends INPUT_FIELD, OPTION_FIELD {
  name: string;
  type: string;
  label: string;
  attributes: ATTRIBUTES[];
  input_class: string;
  input_id: string;
  value: string | any; // Value field for storing the value for download and urls
  validation_schema: {
    required: boolean;
    unique: boolean;
    min_length?: number;
    max_length?: number;
    pattern?: string;
  };
  field_id: string;
  original_field_id: string;
  field_index: number;
  description?: string;
  description_status: boolean;
  is_iterative_or_not?: boolean;
  iteration_min_length?: number;
  iteration_max_length?: number;
  conditions?: FieldCondition[];
  dependencies?: FieldDependency[];
  auto_fill?: FieldInformation;
  auto_fill_dependencies?: FieldInformation[];
}

export interface FieldInformation {
  enabled?: boolean;
  source_form_id?: string;
  source_form_name?: string;
  source_section_name: string;
  source_field_name: string;
  source_field_id: string;
} //Object for store the information regarding field auto fill.

// Conditions structure for controlling field visibility
export interface FieldCondition {
  field_id: string;
  field_name: string;
  field_value: any;
}

// Dependencies structure for dependent fields
export interface FieldDependency {
  selectedFormId: string;
  selectedGroupKey: string;
  selectedInputId: string;
  // field_name: string;
  // field_value: any;
}

export interface QUIZ_FIELD extends INPUT_FIELD, OPTION_FIELD {
  is_quiz_field?: boolean;
  name: string;
  type: string;
  label: string;
  label_url?: string;
  label_url_type?: string;
  attributes: ATTRIBUTES[];
  input_class: string;
  input_id: string;
  value: string | any; // Value field for storing the value for download and urls
  validation_schema: {
    required: boolean;
    unique: boolean;
    min_length?: number;
    max_length?: number;
    pattern?: string;
  };
  field_id: string;
  original_field_id: string;
  field_index: number;
  description?: string;
  description_status: boolean;
  points?: number;
  checkbox_answer_type?: string;
  checkbox_answer_limit?: string;
  is_negative_marking?: boolean;
  negative_marking_points?: number;
  conditions?: FieldCondition[];
  dependencies?: FieldDependency[];
  auto_fill?: FieldInformation;
  auto_fill_dependencies?: FieldInformation[];
}

export interface GROUP {
  // [name: string]: {?
  group_title: string;
  group_key: string;
  group_index: number;
  group_id?: string;
  group_description?: string;
  is_iterative_or_not?: boolean;
  iteration_min_length?: number;
  iteration_max_length?: number;
  fields: Array<FIELD | QUIZ_FIELD>;
  // };
}

export interface VALIDATION_SCHEMA {
  required?: boolean;
  unique?: boolean;
  custom_validation?: CUSTOM_VALIDATION;
  conditional_validation?: CONDITIONAL_VALIDATION;
}

export interface CUSTOM_VALIDATION {
  // File
  maxFiles?: number;
  maxFileSize?: number; // Size in MB's
  fileFormat?: string;

  // Date
  startDate?: string;
  endDate?: string;

  // Time
  startTime?: string;
  endTime?: string;

  // DateTime-local
  startDateTime?: string;
  endDateTime?: string;

  // Number
  minValue?: string;
  maxValue?: string;

  // Phone/fax
  country?: string;

  // Text
  minLength?: string;
  maxLength?: string;
  inputFormat?: string;
}

export interface CONDITIONAL_VALIDATION {
  selectedFormId?: string;
  selectedGroupKey?: string;
  selectedInputId?: string;
  isDisplayed?: string;
  isConditionsRequired?: string;
  dateComparison?: string;
  conditions?: string[];
}

export interface TOTAL_SELECTIONS {
  type?: string;
  value?: number;
}

export enum USER_TYPE {
  user = 'user',
  admin = 'admin',
  orgadmin = 'orgadmin',
}
export interface CREATED_BY {
  user_type: USER_TYPE;
  user_id: string;
  timestamp: number;
  field_id?: string;
}

export enum INPUTS {
  input = 'input',
  toggle = 'toggle',
  select = 'select',
  textarea = 'textarea',
  signature = 'signature',
  address = 'address',
  paragraph = 'paragraph',
  image = 'image',
  editor = 'editor',
}

export enum QUIZ_INPUTS {
  input = 'input',
  select = 'select',
}

export enum INPUT_TYPES {
  text = 'text',
  password = 'password',
  email = 'email',
  number = 'number',
  date = 'date',
  time = 'time',
  datetime = 'datetime-local',
  checkbox = 'checkbox',
  radio = 'radio',
  file = 'file',
  phone = 'phone',
  fax = 'fax',
  url = 'url',
  currency = 'currency',
}

export enum QUIZ_INPUT_TYPES {
  text = 'text',
  number = 'number',
  date = 'date',
  time = 'time',
  datetime = 'datetime-local',
  checkbox = 'checkbox',
  radio = 'radio',
}

export type AUTO_CREATE_FORM = {
  app_id: string;
  name: string;
  form_id: string;
  has_sub_forms: boolean;
  is_sub_form: boolean;
  is_quiz_form?: boolean;
  fields?: any;
  description?: string;
  validationschema?: any;
  status?: boolean;
};

export interface OrgFormsRepositoryWithObjectId extends OrgFormsRepository {
  _id?: string;
}

export type APP_FORMS = {
  name: string;
  app_id: string;
  status: boolean;
  description: string;
  forms: FormsRepository[];
};

export enum QUERY_STRING {
  SUBFORMS = 'sub_forms',
}

// {"data": [{"form": {"name": "Template","fields": {"section_one": {"group_title": "RAM","group_key": "section_one","group_index": 0,"fields": [{"name": "field_one_1711111651847","label": "R","group_title": "RAM","group_key": "section_one"}]}}},"values": {"section_one": {"field_one_1711111651847": "Random "}}}]}

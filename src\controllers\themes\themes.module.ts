import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';

import ThemesController from './themes.controller';
import { AppThemes, Themes } from 'src/entities/themes.entity';
import { OrganizationModule } from '../organization/organization.module';

import { ThemesService } from 'src/services/themes.service';
import { AppsModule } from '../apps/apps.module';
import { UserModule } from '../user/user.module';
import { AppsService } from 'src/services/apps.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { User } from 'src/entities/user.entity';
import { Apps } from 'src/entities/apps.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { Organization } from 'src/entities/organization.entity';
import { OnboardingEmployeeModule } from '../employee/onboarding-employee.module';
import { FormValueService } from 'src/services/formvalues.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { Configurations } from 'src/entities/configurations.entity';
import { SendGridService } from 'src/util/sendgrid.service';
import EncryptionService from 'src/services/encryption.service';
import { PaginationService } from 'src/util/pagination.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import { MongooseModule } from '@nestjs/mongoose';

const imports = [
  UserModule,
  AppsModule,
  OrganizationModule,
  TypeOrmModule.forFeature(
    [
      Themes,
      OnboardingEmployee,
      User,
      Apps,
      Organization,
      OrgAppConfiguration,
      IndustryTypes,
      IndustryAppProcess,
      Configurations,
    ],
    'mysql',
  ),
  MongooseModule.forFeature([
    {
      name: OnBoardEmpChecklistRepository.name,
      schema: OnBoardEmpChecklistRepositorySchema,
    },
    {
      name: OnBoardOrgChecklistRepository.name,
      schema: OnBoardOrgChecklistRepositorySchema,
    },
  ]),
  OnboardingEmployeeModule,
];

const moduleExports = [ThemesService];

@Module({
  imports,
  exports: moduleExports,
  controllers: [ThemesController],
  providers: [
    ThemesService,
    OnboardingEmployeeService,
    AppsService,
    IndustryTypesService,
    IndustryAppProcessService,
    FormValueService,
    ClientsRepositoryService,
    SendGridService,
    EncryptionService,
    PaginationService,
    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
  ],
})
export class ThemesModule {}

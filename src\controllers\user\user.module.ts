import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';

import {
  UserMailExistsRule,
  UserMobileExistsRule,
} from 'src/util/validations/user.validation';
import UserController from './user.controller';
import { User } from 'src/entities/user.entity';
import { AppsModule } from '../apps/apps.module';
import { AdminModule } from '../admin/admin.module';
import { UserService } from 'src/services/user.service';
import { OrganizationModule } from '../organization/organization.module';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { FormValueService } from 'src/services/formvalues.service';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { SendGridService } from 'src/util/sendgrid.service';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import EncryptionService from 'src/services/encryption.service';
import { PaginationService } from 'src/util/pagination.service';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import { CaregiversService } from 'src/services/caregivers.service';
import { Caregivers } from 'src/entities/caregivers.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        User,
        OnboardingEmployee,
        OrgAppConfiguration,
        Configurations,
        IndustryAppProcess,
        Caregivers,
      ],
      'mysql',
    ),
    MongooseModule.forFeature([
      {
        name: OnBoardEmpChecklistRepository.name,
        schema: OnBoardEmpChecklistRepositorySchema,
      },
      {
        name: OnBoardOrgChecklistRepository.name,
        schema: OnBoardOrgChecklistRepositorySchema,
      },
    ]),
    OrganizationModule,
    AdminModule,
    AppsModule,
  ],
  controllers: [UserController],
  providers: [
    UserService,
    ClientsRepositoryService,
    UserMailExistsRule,
    UserMobileExistsRule,
    OnboardingEmployeeService,
    FormValueService,
    SendGridService,
    EncryptionService,
    PaginationService,
    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
    CaregiversService,
  ],
  exports: [UserService],
})
export class UserModule {}

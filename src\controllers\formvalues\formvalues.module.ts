import { Module } from '@nestjs/common';
import { FormValueController } from './formvalues.controller';
import { UserModule } from '../user/user.module';
import { AppsModule } from '../apps/apps.module';
import { OrganizationService } from 'src/services/organization.service';
import { Organization } from 'src/entities/organization.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  OrgFormsRepository,
  OrgFormsRepositorySchema,
} from 'src/entities/mongodb/orgformsrepository.entity';
import {
  FormsRepository,
  FormsRepositorySchema,
} from 'src/entities/mongodb/formsrepository.entity';
import {
  FormValuesRepository,
  FormValuesRepositorySchema,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import { FormValueService } from 'src/services/formvalues.service';
import {
  ClientsRepository,
  ClientsRepositorySchema,
} from 'src/entities/mongodb/clientsrepository.entity';
import { FormsRepositoryService } from 'src/services/formsrespository.service';
import { GlobalService } from 'src/services/global.service';
import { FormFieldsService } from 'src/services/formfields.service';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';
import {
  FormFields,
  FormFieldsSchema,
} from 'src/entities/mongodb/formfields.entity';
import {
  FormSectionsRepository,
  FormSectionsRepositorySchema,
} from 'src/entities/mongodb/form-sections.entity';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { SendGridService } from 'src/util/sendgrid.service';
import { Configurations } from 'src/entities/configurations.entity';
import {
  ESignRequestLogsRepository,
  ESignRequestLogsSchema,
} from 'src/entities/mongodb/e-sign-request-log.entity';
import { ConfigurationsModule } from '../configurations/configurations.module';
import EncryptionService from 'src/services/encryption.service';
import {
  PrimaryFormsRepository,
  PrimaryFormsRepositorySchema,
} from 'src/entities/mongodb/primary-forms-repository.entity';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositorySchema,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { User } from 'src/entities/user.entity';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import { CaregiversModule } from '../caregivers/caregivers.module';

@Module({
  imports: [
    UserModule,
    AppsModule,
    TypeOrmModule.forFeature(
      [
        Organization,
        IndustryTypes,
        OrgAppConfiguration,
        Configurations,
        OnboardingEmployee,
        User,
        IndustryAppProcess,
      ],
      'mysql',
    ),
    CaregiversModule,
    MongooseModule.forFeature([
      { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
      { name: FormsRepository.name, schema: FormsRepositorySchema },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      { name: ClientsRepository.name, schema: ClientsRepositorySchema },
      { name: FormFields.name, schema: FormFieldsSchema },
      { name: ESignRequestLogsRepository.name, schema: ESignRequestLogsSchema },
      {
        name: PrimaryFormsRepository.name,
        schema: PrimaryFormsRepositorySchema,
      },
      {
        name: AppOrgFormsToggleRepository.name,
        schema: AppOrgFormsToggleRepositorySchema,
      },

      {
        name: FormSectionsRepository.name,
        schema: FormSectionsRepositorySchema,
      },
      {
        name: OnBoardEmpChecklistRepository.name,
        schema: OnBoardEmpChecklistRepositorySchema,
      },
      {
        name: OnBoardOrgChecklistRepository.name,
        schema: OnBoardOrgChecklistRepositorySchema,
      },
    ]),
    ConfigurationsModule,
  ],
  exports: [],
  providers: [
    OrganizationService,
    OrgFormsRepositoryService,
    FormValueService,
    FormsRepositoryService,
    GlobalService,
    FormFieldsService,
    FormSectionsRepositoryService,
    ClientsRepositoryService,
    IndustryTypesService,
    SendGridService,
    EncryptionService,
    OnboardingEmployeeService,

    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
  ],
  controllers: [FormValueController],
})
export class FormValuesModule {}

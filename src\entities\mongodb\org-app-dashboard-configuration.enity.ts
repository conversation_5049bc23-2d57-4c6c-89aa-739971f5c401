import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import { profile } from 'console';
import {
  SchemaOptions,
  SchemaTypes,
  ObjectId,
  HydratedDocument,
} from 'mongoose';
import { OrgFormsRepository } from './orgformsrepository.entity';

const OrgAppDashboardConfigurationRepoSchemaOptions: SchemaOptions = {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  collection: 'org_app_dashboard_configuration_repository',
  versionKey: false,
};

enum ListingStyle {
  Grid = 'Grid',
  List = 'List',
}

@Schema(OrgAppDashboardConfigurationRepoSchemaOptions)
export class OrgAppDashboardConfigurationRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ type: SchemaTypes.String, unique: true, required: true })
  configuration_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  organization_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  app_id: string;

  @Prop({
    type: SchemaTypes.String,
    enum: ListingStyle,
    default: ListingStyle.Grid,
  })
  listing_style: ListingStyle;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'OrgFormsRepository',
    required: false,
  })
  form: OrgFormsRepository;

  @Prop({ type: SchemaTypes.Mixed, required: true })
  listing_details: any;

  @Prop({ type: SchemaTypes.Mixed, required: false })
  avatar_field_details: any;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type OrgAppDashboardConfigurationRepositoryDocument =
  HydratedDocument<OrgAppDashboardConfigurationRepository>;

export const OrgAppDashboardConfigurationRepositorySchema =
  SchemaFactory.createForClass(OrgAppDashboardConfigurationRepository);

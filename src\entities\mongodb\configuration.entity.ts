import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import {
  SchemaOptions,
  SchemaTypes,
  ObjectId,
  HydratedDocument,
} from 'mongoose';

const configurationRepoSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'configuration_repository',
  versionKey: false,
};
@Schema(configurationRepoSchemaOptions)
export class ConfigurationRepository {
  @Prop({ type: SchemaTypes.ObjectId })
  id: ObjectId;

  @Prop({ type: SchemaTypes.String, unique: true, required: true })
  configuration_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  organization_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  name: string;

  @Prop({ type: SchemaTypes.String, required: false })
  type: string;

  @Prop({ type: SchemaTypes.Mixed })
  details: any;

  @Prop({ default: true, type: SchemaTypes.Boolean })
  status: boolean;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type ConfigurationRepositoryDocument =
  HydratedDocument<ConfigurationRepository>;

export const ConfigurationRepositorySchema = SchemaFactory.createForClass(
  ConfigurationRepository,
);

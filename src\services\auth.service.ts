import {
  HttpException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Not, Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';

import {
  ADMIN,
  AUTH_TOKEN,
  EMPLOYEE,
  ORGANIZATION,
  USER,
} from 'src/util/interfaces/auth.interface';
import { UserService } from './user.service';
import { AdminService } from './admin.service';
import { User } from 'src/entities/user.entity';
import { Admin } from 'src/entities/admin.entity';
import { updatePasswordDTO } from 'src/dto/auth.dto';
import { OrganizationService } from './organization.service';
import { Organization } from 'src/entities/organization.entity';
import { compareBcryptHash, generateBcryptHash } from 'src/util';
import { OnboardingEmployeeService } from './onboarding-employee.service';
import { EMPLOYEE_STATUS } from 'src/entities/onboarding-employee.entity';
import { CaregiversService } from './caregivers.service';

@Injectable()
export default class AuthService {
  constructor(
    @InjectRepository(Admin, 'mysql')
    private AdminRepository: Repository<Admin>,
    @InjectRepository(Organization, 'mysql')
    private OrganizationRepository: Repository<Organization>,
    @InjectRepository(User, 'mysql')
    private UserRepository: Repository<User>,
    private usersService: UserService,
    private organizationService: OrganizationService,
    private adminService: AdminService,
    private jwtService: JwtService,
    private onboardingEmployeeService: OnboardingEmployeeService,
    private caregiversService:CaregiversService
  ) {}

  /**
   * Validate user was existed or not
   * @param username string
   * @param password string
   * @returns Promise<User | null>
   */
  async validateUser(
    username: string,
    password: string,
    organization_id: string,
  ): Promise<User | null> {
    const organization = await this.organizationService.findOne(
      { organization_id: organization_id },
      false,
      { id: true },
    );

    if (!organization) {
      return null;
    }

    let user: any = await this.usersService.findOne(
      { email: username, organization: organization },
      false,
      {
        ...this.usersService.select,
        password: true,
        id: true,
        organization: {
          organization_id: true,
        },
        apps: true,
      },
      { organization: true, apps: true },
    );

    if (user?.apps?.length) {
      user.apps = user.apps.map((app) => ({ app_code: app.app_code }));
    }

    if (user && (await compareBcryptHash(password, user.password))) {
      return user;
    }
    return null;
  }

  /**
   * Validate organization was existed or not
   * @param username string
   * @param password string
   * @returns Promise<User | null>
   */
  async validateOrganization({ username, password }): Promise<AUTH_TOKEN> {
    const organization = await this.organizationService.findOne(
      {
        email: username,
      },
      false,
      { password: true, ...this.organizationService.select },
    );
    if (
      organization &&
      (await compareBcryptHash(password, organization.password))
    ) {
      return this.generateToken({ ...organization, isOrganization: true });
    }
    throw new UnauthorizedException();
  }

  /**
   * Validate admin was existed or not
   * @param username string
   * @param password string
   * @returns Promise<AUTH_TOKEN>
   */
  async validateAdmin({ username, password }): Promise<AUTH_TOKEN> {
    const admin = await this.adminService.findOne({
      username,
    });
    if (admin && (await compareBcryptHash(password, admin.password))) {
      return this.generateToken({ ...admin, isAdmin: true });
    }
    throw new UnauthorizedException();
  }

  /**
   * Generate jwt token
   * @param user: ADMIN | USER | ORGANIZATION
   * @returns AUTH_TOKEN
   */
  generateToken(user: ADMIN | USER | ORGANIZATION | EMPLOYEE): AUTH_TOKEN {
    try {
      delete user.id;
      delete user.password;
      delete user['bg_verification_document'];
      delete user['scheduled_interviews'];
      delete user['logo'];
      user['organization'] && delete user['organization']['logo'];
      const payload = { ...user };
      return {
        access_token: this.jwtService.sign(payload),
        token_type: 'Bearer',
      };
    } catch (err) {
      throw err;
    }
  }

  /**
   * Update User Password
   * @param body: updatePasswordDTO
   * @returns AUTH_TOKEN
   */
  async updatePassword(body: updatePasswordDTO, userDetails: any) {
    try {
      if (userDetails?.isAdmin) {
        const admin = await this.AdminRepository.findOne({
          where: { admin_id: userDetails.admin_id },
        });

        if (!admin) {
          throw new Error('Admin details not found');
        }

        admin.password = await generateBcryptHash(body.password, 10);

        return await this.AdminRepository.save(admin);
      }

      if (userDetails?.isOrganization) {
        const organization = await this.OrganizationRepository.findOne({
          where: { organization_id: userDetails.organization_id },
        });

        if (!organization) {
          throw new Error('Organization details not found');
        }

        organization.password = await generateBcryptHash(body.password, 10);

        return await this.OrganizationRepository.save(organization);
      }

      if (userDetails?.isUser) {
        const user = await this.UserRepository.findOne({
          where: { user_id: userDetails.user_id },
        });

        if (!user) {
          throw new Error('User details not found');
        }

        user.password = await generateBcryptHash(body.password, 10);

        return await this.UserRepository.save(user);
      }
    } catch (err) {
      throw new HttpException(
        err?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Validate user was existed or not
   * @param username string
   * @param password string
   * @param organization_id string
   * @param app_id string
   * @returns Promise<User | null>
   */
  async validateOnboardingEmployeeOnBoarding(
    username: string,
    password: string,
    organization_id: string,
    app_id: string,
  ) {
    const organization = await this.organizationService.findOne(
      { organization_id: organization_id },
      false,
      { id: true },
    );

    if (!organization) {
      return null;
    }

    const onboarding_employee = await this.onboardingEmployeeService.findOne(
      {
        email: username,
        organization: organization,
        status: Not(EMPLOYEE_STATUS.REJECTED),
      },
      false,
      {
        ...this.onboardingEmployeeService.select,
        password: true,
        id: true,
        organization: {
          organization_id: true,
        },
      },
      { organization: true },
    );

    if (
      onboarding_employee &&
      (await compareBcryptHash(password, onboarding_employee.password))
    ) {
      return {
        ...onboarding_employee,
        isOnboardingEmployee: true,
        status: onboarding_employee.status,
        user_id: onboarding_employee.onboarding_employee_id,
        app_id,
      };
    }

    return null;
  }

  /**
   * Validate user was existed or not
   * @param username string
   * @param password string
   * @param organization_id string
   * @param app_id string
   * @returns Promise<User | null>
   */
  async validateCaregiver(
    username: string,
    password: string,
    organization_id: string,
    app_id: string,
  ) {
    const organization = await this.organizationService.findOne(
      { organization_id: organization_id },
      false,
      { id: true },
    );

    if (!organization) {
      return null;
    }

    const caregiver = await this.caregiversService.findOne(
      {
        email: username,
        organization: organization,
        
      },
      false,
      {
        ...this.caregiversService.select,
        password: true,
        id: true,
        organization: {
          organization_id: true,
        },
      },
      { organization: true },
    );

    if (
      caregiver &&
      (await compareBcryptHash(password, caregiver.password))
    ) {
      return {
        ...caregiver,
        isCaregiver: true,
        user_id: caregiver.caregiver_id,
        app_id,
      };
    }

    return null;
  }
}

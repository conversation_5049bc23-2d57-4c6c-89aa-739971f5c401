import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  UseInterceptors,
  applyDecorators,
} from '@nestjs/common';
import * as _ from 'lodash';
import { Observable } from 'rxjs';
import { REQUEST_CONTEXT } from '../constants';

@Injectable()
class InjectPathParamsInterceptor implements NestInterceptor {
  constructor(private type?: 'query' | 'body' | 'params') {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    if (this.type && request[this.type]) {
      request[this.type][REQUEST_CONTEXT] = {
        ...request[this.type][REQUEST_CONTEXT],
        pathparams: request.params,
      };
    }

    return next.handle();
  }
}

export function InjectPathParsmToQuery() {
  return applyDecorators(InjectPathParamsTo('query'));
}

export function InjectPathParsmToBody() {
  return applyDecorators(InjectPathParamsTo('body'));
}

export function InjectPathParsmToParam() {
  return applyDecorators(InjectPathParamsTo('params'));
}

export function InjectPathParamsTo(context: 'query' | 'body' | 'params') {
  return applyDecorators(
    UseInterceptors(new InjectPathParamsInterceptor(context)),
  );
}

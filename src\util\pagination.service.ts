import { Injectable } from '@nestjs/common';
import { PaginationDto } from 'src/dto/pagination.dto';

@Injectable()
export class PaginationService {
  async paginate(model: any, query: PaginationDto, options: any = {}) {
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    // Handle both Mongoose and TypeORM
    if (model.constructor.name === 'Function') {
      const data = await model
        .find(options.where || {})
        .skip(skip)
        .limit(limit)
        .select(options.select || {})
        .sort(query.sort || { created_at: -1 });

      const total = await model.countDocuments(options.where || {});

      return {
        data,
        meta: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      };
    } else {
      delete options?.select;
      // TypeORM
      const [data, total] = await model.findAndCount({
        ...options,
        skip,
        take: limit,
        order: query.sort || { created_at: 'DESC' },
      });

      return {
        data,
        meta: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      };
    }
  }
}

import {
  HttpStatus,
  HttpException,
  Injectable,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  In,
  Not,
  Repository,
} from 'typeorm';

import { UserDto, UserRegisterDto } from 'src/dto/user.dto';
import { User } from 'src/entities/user.entity';
import { OrganizationService } from './organization.service';
import { generateBcryptHash, generateUUID } from 'src/util';
import { AppsService } from './apps.service';
import { OrgFormsRepository } from 'src/entities/mongodb/orgformsrepository.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { SendGridService } from 'src/util/sendgrid.service';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { OnboardingEmployeeService } from './onboarding-employee.service';
import { PaginationService } from 'src/util/pagination.service';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User, 'mysql')
    private UserRepository: Repository<User>,
    @InjectRepository(OrgAppConfiguration, 'mysql')
    private orgAppConfiguration: Repository<OrgAppConfiguration>,
    private organizationService: OrganizationService,
    private appsService: AppsService,
    private sendGridService: SendGridService,
    private paginationService: PaginationService,
    @Inject(forwardRef(() => OnboardingEmployeeService))
    private readonly onboardingEmployeeService: OnboardingEmployeeService,
  ) {}

  /**
   * @variable select FindOptionsSelect<User>
   */
  public select: FindOptionsSelect<User> = {
    name: true,
    email: true,
    user_id: true,
    password: false,
    created_at: true,
    updated_at: true,
    mobile_number: true,
  };

  /**
   * Create user record
   * @param data UserRegisterDto
   * @returns Promise<User>
   */
  async create(data: UserRegisterDto): Promise<User> {
    try {
      const raw_password = data.password;

      data.password = await generateBcryptHash(data.password, 10);

      const organization = await this.organizationService.findOne(
        {
          organization_id: data.organization,
        },
        false,
        {
          id: true,
          name: true,
          email: true,
          organization_id: true,
          pass_key: true,
        },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const {
        name: organizationName,
        email: organizationEmail,
        organization_id,
        pass_key,
      } = organization;

      data.organization = organization;

      const apps = await this.appsService.list(
        { app_id: In(data.apps) },
        { id: true },
      );

      if (apps.length !== data.apps.length) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: {
            statusCode: HttpStatus.BAD_REQUEST,
            message: ['Please select the existed App only.'],
            error: 'Bad Request',
          },
        };
      }

      const saveData = {
        ...data,
        apps,
        user_id: generateUUID(),
      };

      const user = await this.UserRepository.save(saveData);

      try {
        //TODO: Need to add link for Assessment Application
        const mail = {
          to: {
            email: user['email'],
            name: organizationName
              ? organizationName.toUpperCase()
              : 'The Kleza FAB Team',
          },
          subject: `Welcome to ${
            organizationName ? organizationName.toUpperCase() : 'Kleza FAB'
          }!`,
          from: '',
          text: 'Hai',
          html: `
            <html>
              <head></head>
              <body>
                <h5>Hi ${user?.name?.toUpperCase()}, Welcome to ${
            organizationName ? organizationName.toUpperCase() : 'Kleza FAB'
          }! </h5>
                <br>
                <p>You have been added to our system by your Organization's Admin. Below, you will find your account details and information about the apps you are authorized to access. </p>
                <br/>
                <p>Your Account Details: </p>
                <br/>
                <p><b>Username:</b> ${user.email}</p>
                <p><b>Temporary Password:</b> ${raw_password}</p>
                <p><b>Passkey:</b> ${pass_key}</p>
                <p style="font-size: 12px; color: gray;">Tip: Select the text and press Ctrl+C (Cmd+C on Mac) to copy.</p>
                <p><b>Download the "Client Assessment" application:</b></p>
                <ul>
                  <li>
                    <b>Android (Play Store):</b>
                    <a href="https://play.google.com/store/apps/details?id=com.klezafab.assessment" target="_blank">
                      Download on Play Store
                    </a>
                  </li>
                  <li>
                    <b>iOS (App Store):</b>
                    <a href="https://apps.apple.com/in/app/client-assessment/id6744399669" target="_blank">
                      Download on App Store
                    </a>
                  </li>
                </ul>
              <br/>
                <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:${organizationEmail}">${organizationEmail}</a> </p>
                <br/>
                <p>Thank you for joining ${
                  organizationName
                    ? organizationName.toUpperCase()
                    : 'Kleza FAB'
                } </p>
                <p>Best regards, </p>
                <p>HR Manager, </p>
                <p>${
                  organizationName
                    ? organizationName.toUpperCase()
                    : 'The Kleza FAB Team'
                } </p>
              </body>
            </html>
          `,
        };

        await this.sendGridService.sendWithOrganizationId(
          organization_id,
          mail,
        );
      } catch (error) {
        console.log('Error in sending mail', error);
      }

      return user;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user record by condition
   * @param findBy Object "{column: value}"
   * @returns Promise<User>
   */
  async findOne(
    findBy: FindOptionsWhere<User>,
    withDeleted = false,
    select: FindOptionsSelect<User> = this.select,
    relations: FindOptionsRelations<User> = {},
  ): Promise<User> {
    try {
      const relationKeys = Object.keys(relations);

      if (relationKeys.length > 0) {
        if (relationKeys.includes('apps')) {
          select = {
            ...select,
            organization: { id: true, ...this.organizationService.select },
          };
        }
      }

      const user = await this.UserRepository.findOne({
        select,
        relations,
        withDeleted,
        where: findBy,
      });

      if (relationKeys.length > 0) {
        if (relationKeys.includes('apps') && user?.apps && user?.apps?.length) {
          user.apps = await Promise.all(
            user.apps.map(async (app: any) => {
              app.orgAppConfiguration = await this.orgAppConfiguration.findOne({
                where: {
                  app: { id: app.id },
                  organization: { id: user.organization.id },
                },
              });
              delete app?.id;
              return app;
            }),
          );
        }
      }

      return user;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Check email was existed in user record or not
   * @param email string
   * @param id string
   * @param organization_id string
   * @returns Promise<User>
   */
  async checkExistedUserORNot(
    email: string,
    id?: string,
    organization_id?: string,
  ): Promise<User> {
    const find: FindOptionsWhere<User> = { email };
    if (id) find.user_id = Not(id);
    if (organization_id) {
      const organization = await this.organizationService.findOne(
        { organization_id },
        false,
        { id: true },
      );
      find.organization = organization;
    }
    return await this.findOne(find, false, { ...this.select, id: true });
  }
  /**
   * Check mobile number was existed in user record or not
   * @param mobile_number string
   * @returns Promise<User>
   */
  async checkExistedUserMobileORNot(
    mobile_number: string,
    id?: string,
    organization_id?: string,
  ): Promise<User> {
    const find: FindOptionsWhere<User> = { mobile_number };
    if (id) find.user_id = Not(id);
    if (organization_id) {
      const organization = await this.organizationService.findOne(
        { organization_id },
        false,
        { id: true },
      );
      find.organization = organization;
    }
    return await this.findOne(find, false, { ...this.select, id: true });
  }

  /**
   * Update the user record by id
   * @param data UserDto
   * @returns Promise<User>
   */
  async update(data: UserDto, id: string): Promise<User> {
    try {
      if (await this.checkExistedUserORNot(data.email, id, data.organization))
        throw new HttpException(
          'Entered email id was already existed.',
          HttpStatus.CONFLICT,
        );

      if (
        await this.checkExistedUserMobileORNot(
          data.mobile_number,
          id,
          data.organization,
        )
      )
        throw new HttpException(
          'Entered mobile number was already existed.',
          HttpStatus.CONFLICT,
        );

      const organization = await this.organizationService.findOne(
        {
          organization_id: data.organization,
        },
        true,
        { id: true },
      );

      if (!organization) throw new NotFoundException(`Organization Not Found`);
      const user = await this.findOne({ user_id: id, organization }, false, {
        ...this.select,
        id: true,
      });

      if (!user) throw new NotFoundException(`User Not Found`);

      if (data.password)
        data.password = await generateBcryptHash(data.password, 10);
      const saveData: any = {
        id: user.id,
        ...data,
        organization,
      };

      if (data.apps?.length) {
        const apps = await this.appsService.list(
          { app_id: In(data.apps) },
          { id: true },
        );
        if (apps.length !== data.apps.length) {
          throw {
            status: HttpStatus.BAD_REQUEST,
            message: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: ['Please select the existed App only.'],
              error: 'Bad Request',
            },
          };
        }
        saveData.apps = apps;
      }

      return await this.UserRepository.save(saveData, { reload: true });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get All users
   * @returns User[]
   */
  async list(
    organization_id?: string,
    pagination?: boolean,
    page?: number,
    limit?: number,
  ): Promise<any> {
    const findBy: FindOptionsWhere<User> = {};

    if (organization_id) {
      findBy.organization = await this.organizationService.findOne(
        {
          organization_id,
        },
        false,
        { id: true },
      );
    }

    if (pagination) {
      const usersWithPagination = await this.paginationService.paginate(
        this.UserRepository,
        { page, limit },
        {
          where: findBy,
          relations: {
            organization: true,
            apps: true,
            onboarding_employee: true,
          },
          select: this.select,
        },
      );

      if (
        usersWithPagination &&
        usersWithPagination?.data?.length &&
        organization_id
      ) {
        usersWithPagination.data = await Promise.all(
          usersWithPagination?.data?.map(async (user: any) => {
            const updatedApps = await Promise.all(
              user.apps.map(async (app: any) => {
                app.orgAppConfiguration =
                  await this.orgAppConfiguration.findOne({
                    where: {
                      app: { id: app.id },
                      organization: findBy.organization,
                    },
                  });
                delete app?.id;
                return app;
              }),
            );
            user.apps = updatedApps;
            return user;
          }),
        );
      }

      return usersWithPagination;
    } else {
      let users = await this.UserRepository.find({
        where: findBy,
        relations: {
          organization: true,
          apps: true,
          onboarding_employee: true,
        },
        select: this.select,
      });

      if (users.length && organization_id) {
        users = await Promise.all(
          users.map(async (user: any) => {
            const updatedApps = await Promise.all(
              user.apps.map(async (app: any) => {
                app.orgAppConfiguration =
                  await this.orgAppConfiguration.findOne({
                    where: {
                      app: { id: app.id },
                      organization: findBy.organization,
                    },
                  });
                delete app?.id;
                return app;
              }),
            );
            user.apps = updatedApps;
            return user;
          }),
        );
      }

      return users;
    }
  }

  /**
   * Delete user by id
   * @param user_id string ID of the user
   * @param organization_id string
   * @returns Promise<Boolean>
   */
  async delete(user_id: string, organization_id?: string): Promise<Boolean> {
    try {
      const findBy: FindOptionsWhere<User> = {
        user_id,
      };
      if (organization_id) {
        const organization = await this.organizationService.findOne(
          {
            organization_id,
          },
          false,
          { id: true },
        );
        findBy.organization = organization;
      }

      const user = await this.findOne(findBy, false, {
        ...this.select,
        id: true,
      });

      if (!user) throw new NotFoundException('User not found');
      return this.UserRepository.softDelete(findBy) ? true : false;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import {
  Body,
  HttpException,
  HttpStatus,
  Post,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Controller,
  <PERSON><PERSON>,
  Req,
  Patch,
  Param,
  Get,
  Delete,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request, Response } from 'express';
import { QueryOptions } from 'mongoose';
import {
  FormSectionRepoDto,
  UpdateFormSectionRepoDto,
} from 'src/dto/formrepository.dto';
import { FormSectionsRepository } from 'src/entities/mongodb/form-sections.entity';
import { AdminAuth } from 'src/guards/admin.guard';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';

@Controller('forms-section-repository')
export class FormSectionRepositoryController {
  constructor(
    private readonly formSectionsRepoService: FormSectionsRepositoryService,
  ) {}

  /**
   * Create the form
   * @param body FormsRepoDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Post()
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'))
  async create(
    @Req() request: Request,
    @Body() body: FormSectionRepoDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      return response.send({
        status: true,
        message: 'Form section created successfully',
        data: await this.formSectionsRepoService.create(body),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the from section by section_id
   * @param body FormSectionRepoDto
   * @param section_id string
   * @param response Response
   * @returns Promise<FormSectionsRepository>
   */
  @Patch('/:section_id')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(ValidationPipe)
  async update(
    @Body() body: UpdateFormSectionRepoDto,
    @Param('section_id') section_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      return response.send({
        status: true,
        message: 'Form section updated successfully',
        data: await this.formSectionsRepoService.update(body, section_id),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * get the from section by section_id
   * @param section_id string
   * @param response Response
   * @returns Promise<FormSectionsRepository>
   */
  @Get('/:section_id')
  @UseGuards(AuthGuard('jwt'))
  async findOne(
    @Param('section_id') section_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.formSectionsRepoService.findOne({ section_id });

      return data
        ? response.send({
            status: true,
            message: 'Form section details fetched Successfully',
            data: data,
          })
        : response.status(500).send({
            status: false,
            message: 'Something went wrong. Please try again later.',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * get the from section by section_id
   * @param section_id string
   * @param response Response
   * @returns Promise<FormSectionsRepository>
   */
  @Get('')
  @UseGuards(AuthGuard('jwt'))
  async findAll(
    @Res() response: Response,
    @Query('name') name?: string,
  ): Promise<Response> {
    try {
      const condition: QueryOptions<FormSectionsRepository> = { status: true };

      if (name) {
        condition.name = { $regex: new RegExp(name, 'i') };
      }

      const data = await this.formSectionsRepoService.findAll(condition);

      return response.send({
        status: true,
        message: 'Form sections fetched Successfully',
        data: data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete the form section
   * @param section_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Delete('/:section_id')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(ValidationPipe)
  async deleteField(
    @Param('section_id') section_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.formSectionsRepoService.delete(section_id);
      return data
        ? response.send({
            status: true,
            message: 'Section is delete successfully',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to delete the Section',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import {
  Body,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Controller,
  NotFoundException,
  Res,
  Patch,
  Delete,
  Req,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  AutoFieldDto,
  AutoCreateFormsRepoDto,
  FormDetails,
  FormsRepoDto,
  UpdateFormsRepoDto,
  UpdateField,
  DeleteFieldDto,
  UpdateIndex,
  CopyFieldDto,
  AddSectionDto,
  RemoveSectionDto,
  UpdateGroupIndex,
  UpdateFormsIndex,
  CustomValidationBody,
} from 'src/dto/formrepository.dto';
import { FormsRepositoryService } from 'src/services/formsrespository.service';
import { getFormField } from 'src/util/constants';
import { ObjectID } from 'mongodb';
import { Request, Response } from 'express';
import {
  FIELD,
  GROUP,
  QUERY_STRING,
} from 'src/util/interfaces/forms.interface';
import { AdminOrOrganizationAuth } from 'src/guards/adminOrOrganization.guard';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { InjectUserToBody } from 'src/util/interceptor/user.interceptor';
import { UserAuth } from 'src/guards/user.guard';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { ConfigurationsService } from 'src/services/configurations.service';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositoryDocument,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Controller('forms-repository')
export class FormsRepositoryController {
  constructor(
    @InjectModel(AppOrgFormsToggleRepository.name)
    private appOrgFormsToggleRepository: Model<AppOrgFormsToggleRepositoryDocument>,

    private readonly frService: FormsRepositoryService,
    private readonly orgFormsService: OrgFormsRepositoryService,
    private readonly clientsService: ClientsRepositoryService,
    private readonly configurationsService: ConfigurationsService,
  ) {}

  /**
   * Create the form
   * @param body FormsRepoDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('/')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async create(
    @Req() request: Request,
    @Body() body: FormsRepoDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let form = await this.frService.create(body);
      if (request.user['isOrganization']) {
        form = await this.orgFormsService.addForm(
          request.user['organization_id'],
          form,
          request.user,
        );
      }
      return form
        ? response.send({
            status: true,
            message: 'Form created successfully',
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to create form' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the index of forms from app with in the organization
   * @param body UpdateFormsIndex
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('update-forms-index/:app_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async updateFormsIndex(
    @Param('app_id') app_id: string,
    @Body() body: UpdateFormsIndex,
    @Res() response: Response,
    @Req() request: Request,
  ) {
    try {
      let data: any;

      if (request.user['isOrganization']) {
        data = await this.orgFormsService.updateFormsIndex(body);
      } else {
        data = await this.frService.updateFormsIndex(body);
      }
      return response.send({
        status: true,
        message: 'Forms order successfully updated.',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the index of forms from app with in the organization
   * @param body UpdateFormsIndex
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('toggle-forms-fill-order/:app_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async toggleFormsFillOrder(
    @Param('app_id') app_id: string,
    @Query('form_id') form_id: string,
    @Res() response: Response,
    @Req() request: Request,
  ) {
    try {
      let data: any;

      if (request.user['isOrganization']) {
        const organization_id = request.user['organization_id'];

        data = await this.orgFormsService.toggleFormsFillOrder(
          app_id,
          organization_id,
          form_id,
        );
      }

      return response.send({
        status: true,
        message: 'Set forms order toggle updated successfully',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Auto Create Form
   * @param body CreateFormsRepo
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('auto-create')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @InjectUserToBody()
  async autoCreate(
    @Req() request: Request,
    @Body() body: AutoCreateFormsRepoDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let data;
      if (request.user['isOrganization']) {
        data = await this.orgFormsService.autoCreate(
          body,
          request.user['organization_id'],
        );
      } else {
        data = await this.frService.autoCreate(body);
      }
      return data
        ? response.send({
            status: true,
            message: 'Form created successfully.',
            data,
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to add field.' });
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Patch the form details except fields
   * @param form_id string
   * @param body FormDetails
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('/:form_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async patchForm(
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Body() body: FormDetails,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const user = request.user;
      let data;
      if (user['isOrganization']) {
        data = await this.orgFormsService.patchForm(
          user['organization_id'],
          form_id,
          body,
        );
      } else {
        data = await this.frService.patchForm(form_id, body);
      }
      return data
        ? response.send({
            status: true,
            message: 'Form updated successfully.',
            data,
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to update form.' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Add Field to Form
   * @param form_id string
   * @param body AutoFieldDto
   * @param response Response
   * @returns Promiese<Response>
   */
  @Patch('add-field/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async addField(
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Body() body: AutoFieldDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const user = request.user;
      let data;
      if (user['isOrganization']) {
        data = await this.orgFormsService.addField(
          user['organization_id'],
          form_id,
          body,
        );
      } else {
        data = await this.frService.addField(form_id, body);
      }
      return data
        ? response.send({
            status: true,
            message: 'Field add successfully.',
            data,
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to add field.' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Add Section to Form
   * @param form_id string
   * @param body AddSectionDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('add-section/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async addSection(
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Body() body: AddSectionDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const user = request.user;

      const data = await this.frService.addSection(form_id, body);

      return data
        ? response.send({
            status: true,
            message: 'Section add successfully.',
            data,
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to add field.' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Remove Section to Form
   * @param form_id string
   * @param body RemoveSectionDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('remove-section/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async removeSection(
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Body() body: RemoveSectionDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const user = request.user;
      const data = await this.frService.removeSection(form_id, body);
      return data
        ? response.send({
            status: true,
            message: 'Section add successfully.',
            data,
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to add field.' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Duplicate / Copy Section to Form
   * @param form_id string
   * @param body any
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('duplicate-section/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async duplicateSection(
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Body() body: any,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const user = request.user;

      let data;

      if (user['isOrganization']) {
        data = await this.orgFormsService.duplicateSection(form_id, body);
      } else {
        data = await this.frService.duplicateSection(form_id, body);
      }

      return data
        ? response.send({
            status: true,
            message: 'Section duplicated successfully.',
            data,
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to duplicate section.' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Copy the field based on requested copy details
   * @param form_id string
   * @param body CopyFieldDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('copy-field/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async copyField(
    @Param('form_id') form_id: string,
    @Body() body: CopyFieldDto,
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<Response> {
    try {
      const user = request.user;
      let data;
      if (user['isOrganization']) {
        data = await this.orgFormsService.copyField(
          user['organization_id'],
          form_id,
          body,
        );
      } else {
        data = await this.frService.copyField(form_id, body);
      }
      return response.send({
        status: true,
        message: 'Copying field was successfully',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the field in the form using form_id
   * @param form_id string
   * @param body UpdateField
   * @param response Response
   * @returns Promise<Respone>
   */
  @Patch('/update-field/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async updateField(
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Body() body: UpdateField,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const user = request.user;
      let data;
      if (user['isOrganization']) {
        data = await this.orgFormsService.updateField(
          user['organization_id'],
          form_id,
          body,
        );
      } else {
        data = await this.frService.updateField(form_id, body);
      }
      return response.send({ status: true, data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the index of form field or section
   * @param form_id string
   * @param body UpdateIndex
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('/update-index/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async updateIndex(
    @Param('form_id') form_id: string,
    @Body() body: UpdateIndex,
    @Res() response: Response,
    @Req() request: Request,
  ) {
    try {
      let data;
      if (request.user['isOrganization']) {
        data = await this.orgFormsService.updateIndex(form_id, body);
      } else {
        data = await this.frService.updateIndex(form_id, body);
      }
      return response.send({
        status: true,
        message: 'Indexing successfully updated',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the index of form field or section
   * @param form_id string
   * @param body UpdateIndex
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('/update-group-index/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async updateGroupsIndex(
    @Param('form_id') form_id: string,
    @Body() body: UpdateGroupIndex,
    @Res() response: Response,
    @Req() request: Request,
  ) {
    try {
      let data;
      if (request.user['isOrganization']) {
        data = await this.orgFormsService.updateGroupsIndex(form_id, body);
      } else {
        data = await this.frService.updateGroupsIndex(form_id, body);
      }
      return response.send({
        status: true,
        message: 'Indexing successfully updated',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete the field in form
   * @param form_id string
   * @param body DeleteFieldDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Delete('/delete-field/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async deleteField(
    @Param('form_id') form_id: string,
    @Body() body: DeleteFieldDto,
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<Response> {
    try {
      const user = request.user;
      let data;
      if (user['isOrganization']) {
        data = await this.orgFormsService.deleteField(
          form_id,
          body,
          user['organization_id'],
        );
      } else {
        data = await this.frService.deleteField(form_id, body);
      }
      return data
        ? response.send({
            status: true,
            message: `${
              body.field_id ? 'Field' : 'Section'
            } is delete successfully.`,
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: `Unable to delete the ${
              body.field_id ? 'Field' : 'Section'
            }`,
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get the forms list
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getList(
    @Query('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let data: any;
      if (request.user['isOrganization']) {
        data = await this.orgFormsService.getAllFormsByApps(
          request.user['organization_id'],
          app_id,
        );
      } else {
        data = await this.frService.getAllFormsByApps(app_id);
      }
      return response.send({
        status: true,
        message: 'Forms list get successfully',
        /* data: await this.frService.list(
          { status: true, deleted_at: null },
          {
            name: true,
            status: true,
            form_id: true,
            createdAt: true,
            updatedAt: true,
          },
        ), */
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get the signature form list
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/signature-forms')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async getSignatureFormsList(
    @Query('client_id') client_id: string,
    @Req() request: any,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const app_code = request.headers['kleza-app-code'];

      const organization_id = request.user['organization'];

      const data = await this.orgFormsService.getSignatureFormsList(
        app_code,
        organization_id,
        client_id,
      );

      return response.send({
        status: true,
        message: 'Forms list get successfully',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get the signature form list with pulic
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/signature-forms/:request_id')
  async getSignatureFormsListPublic(
    @Param('request_id') request_id: string,
    @Req() request: any,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.orgFormsService.getSignatureFormsListPublic(
        request_id,
      );

      return response.send({
        status: true,
        message: 'Forms list get successfully',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get the signature form list
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('/e-sign-request')
  @UseGuards(AuthGuard('jwt'), UserAuth)
  async eSignRequest(
    @Body() body: any,
    @Req() request: any,
    @Res() response: Response,
  ): Promise<any> {
    try {
      body.organization_id = request.user['organization'];

      body.app_code = request.headers['kleza-app-code'];

      body.user_id = request.user['user_id'];

      const result = await this.orgFormsService.eSignRequest(body);

      return response.status(result.status ? 200 : 400).json(result);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/ui')
  async ui(@Query('form_id') id, @Query('edit_id') edit_id) {
    if (id) {
      const form: any = await this.frService.findOne({ id });
      if (form) {
        let th = '';
        const values = await this.frService.valuesList();
        form.actionURL = `/forms-repository/save/${form.id}`;
        const columnIndex = [];
        // return typeof form.columns;
        const html = form.columns
          .map((column) => {
            columnIndex.push(column.name);
            th += `<th style="padding: 5px 25px;">${column.label}</th>`;
            return getFormField(column);
          })
          .join('');
        if (th) {
          th += `<th style="padding: 5px 25px;">Action</th>`;
        }
        let td = '';
        if (values.length) {
          td = values
            .map(({ values, id }) => {
              return `<tr>${columnIndex
                .map((column) => {
                  return `<td>${values[column]}</td>`;
                })
                .join('')}
              <td><a href="/forms-repository/ui?edit_id=${id}">Edit</a></td>
              </tr>`;
            })
            .join('');
        }
        const table = `<table border="1" style="border-collapse: collapse;">${th}${td}</table>`;
        if (html) {
          return `<html>
                    <head>
                      <title>Forms</title>
                    </head>
                    <body>
                      <form action="${form.actionURL}" method="POST">
                        ${html}
                        <input type="submit" value="Submit">
                      </form>
                      <div style="margin-top: 50px;">
                        ${table}
                      </div>
                    </body>
                  </html>`;
        }
      }
      return { status: false, message: 'Form Not Found' };
    } else if (edit_id) {
      const form: any = await this.frService.getFormDataWithForm(id);
      if (form) {
        form.actionURL = `/forms-repository/update/${edit_id}`;
        const html = form.data.columns
          .map((column) => getFormField(column))
          .join('');
        if (html) {
          return `<html>
                      <head>
                        <title>Forms</title>
                      </head>
                      <body>
                        <form action="${form.actionURL}" method="POST">
                          ${html}
                          <input type="submit" value="Submit">
                        </form>
                      </body>
                    </html>`;
        }
      }
    } else {
      const forms = await this.frService.list();
      let options = '<option>Select Form</option>';
      forms.forEach((form) => {
        options += `<option value=${form.id}>${form.name}</option>`;
      });
      return `<html>
                <head>
                  <title>Forms</title>
                </head>
                <body>
                  <form>
                    <select name="form_id">${options}</select>
                    <input type="submit" value="Get">
                  </form>
                </body>
              </html>`;
    }
  }

  /**
   * Get form by form_id
   * @param form_id string
   * @param response Response
   * @param expand: QUERY_STRING
   * @returns Promise<Response>
   */
  @Get('/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getForm(
    @Req() request: Request,
    @Param('form_id') form_id: string,
    @Res() response: Response,
    @Query('expand') expand: QUERY_STRING,
  ): Promise<Response> {
    try {
      const select = this.frService.select;

      let queryExpand: any = expand;

      if (
        expand &&
        (expand.includes('sub_forms') || expand.includes('subforms'))
      ) {
        select.sub_forms = true;
        queryExpand = {
          path: expand,
          select: 'form_id name status has_sub_forms',
        };
      }

      let data;

      if (request.user['isOrganization']) {
        const orgSelect = this.orgFormsService.select;
        data = await this.orgFormsService.findOne(
          {
            form_id,
            organization: request.user['organization_id'],
          },
          false,
          orgSelect,
          queryExpand,
        );
      } else {
        data = await this.frService.findOne(
          { form_id },
          false,
          select,
          queryExpand,
        );
      }

      if (!data) {
        throw new NotFoundException();
      }
      let groups;

      if (!data.has_sub_forms) {
        groups = Object.keys(data.fields).map((key: string): GROUP => {
          const fields = data.fields[key]?.fields?.length
            ? data.fields[key]?.fields
                ?.slice()
                ?.sort((f1: FIELD, f2: FIELD) =>
                  f1.field_index > f2.field_index ? 1 : -1,
                )
            : [];
          return {
            ...data.fields[key],
            fields: fields,
            group_key: key,
            group_description: data?.fields[key]?.group_description || '',
          };
        });
      }

      const responseData: any = {
        groups,
        name: data.name,
        icon: data?.icon,
        description: data?.description || '',
        app_id: data.app_id,
        status: data.status,
        is_disable_form_response: data?.is_disable_form_response || false,
        has_multiple_form_responses: data?.has_multiple_form_responses || false,
        form_id: data.form_id,
        has_sub_forms: data.has_sub_forms,
        is_quiz_form: data?.is_quiz_form || false,
        sectionViewType: data?.sectionViewType,
        tabletLayout: data?.tabletLayout,
        form_response_as_email: data?.form_response_as_email,
      };

      if (
        expand &&
        (expand.includes('sub_forms') || expand.includes('subforms'))
      ) {
        responseData.sub_forms = data.sub_forms;
      }
      return response.send({
        status: true,
        message: 'Form get successfully',
        data: responseData,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get subforms by form_id
   * @param form_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get(':form_id/sub-forms')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getSubForms(
    @Req() request: Request,
    @Param('form_id') form_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let data: any;

      if (request.user['isOrganization']) {
        data = await this.orgFormsService.findOne(
          {
            form_id,
            has_sub_forms: true,
            organization: request.user['organization_id'],
          },
          false,
          {
            sub_forms: true,
            app_id: true,
            form_id: true,
            _id: false,
            order_position: true,
            name: true,
            status: true,
            icon: true,
          },
          {
            path: 'sub_forms',
            select: 'form_id name status has_sub_forms order_position icon',
          },
        );
      } else {
        data = await this.frService.findOne(
          { form_id, has_sub_forms: true },
          false,
          {
            sub_forms: true,
            app_id: true,
            form_id: true,
            _id: false,
            name: true,
            status: true,
            icon: true,
          },
          {
            path: 'sub_forms',
            select: 'form_id name status has_sub_forms order_position icon',
          },
        );
      }

      if (!data) {
        throw new NotFoundException();
      }

      let sub_forms = data.sub_forms.map((form: any) => {
        return {
          form_id: form.form_id,
          name: form.name,
          status: form.status,
          order_position: form?.order_position,
          icon: form?.icon,
        };
      });

      let toggle_forms_fill_order_status: boolean = false;

      if (request.user['isOrganization']) {
        const toggleDetails: AppOrgFormsToggleRepository =
          await this.appOrgFormsToggleRepository.findOne({
            app_id: data.app_id,
            organization_id: request?.user['organization_id'],
            form_id: data.form_id,
          });

        if (toggleDetails && toggleDetails.status) {
          sub_forms = sub_forms.sort(
            (formA: any, formB: any) =>
              formA.order_position - formB.order_position,
          );
        }

        toggle_forms_fill_order_status = toggleDetails?.status || false;
      }

      const responseData: any = {
        app_id: data.app_id,
        main_form_id: data.form_id,
        name: data?.name,
        icon: data?.icon,
        description: data?.description || '',
        status: data.status,
        form_id: data.form_id,
        has_sub_forms: data.has_sub_forms,
        is_quiz_form: data?.is_quiz_form || false,
        sectionViewType: data?.sectionViewType,
        tabletLayout: data?.tabletLayout,
        toggle_forms_fill_order_status,
        sub_forms,
      };

      return response.send({
        status: true,
        message: 'Form get successfully',
        data: responseData,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Change the form status
   * @param id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Put('/change-status/:id')
  @UseGuards(AuthGuard('jwt'))
  async changeStatus(
    @Param('id') id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const { body } = request;
      let data;
      if (request.user['isOrganization']) {
        data = await this.orgFormsService.changeStatus(id, body);
      } else {
        data = await this.frService.changeStatus(id, body);
      }
      return data
        ? response.send({
            status: true,
            data: data,
            message: 'Form status changed successfully.',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to change the form status.',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the form
   * @param body FormsRepoDto
   * @param field_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Put('/:form_id')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(ValidationPipe)
  async updateForm(
    @Body() body: UpdateFormsRepoDto,
    @Param('form_id') form_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.frService.update(body, form_id);
      return data
        ? response.send({
            status: true,
            message: 'Form updated successfully',
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to update the form' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Validate preview api
   * @param body any
   * @param form_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('preview-validate/:form_id')
  @UseGuards(AuthGuard('jwt'))
  @UsePipes(ValidationPipe)
  async previewValidate(
    @Body() body: any,
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      let data;
      if (request?.user['isOrganization']) {
        data = await this.orgFormsService.validatePreview(form_id, body);
      } else {
        data = await this.frService.validatePreview(form_id, body);
      }
      if (typeof data === 'boolean') {
        return response.send({
          status: true,
          message: 'Form is working fine.',
        });
      } else {
        return response.send({
          status: true,
          data,
          message: 'Form is working fine with validations',
        });
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('save/:id')
  // @UseGuards(AuthGuard('jwt'))
  async saveFormValues(@Body() body, @Param('id') id: ObjectID, @Res() res) {
    try {
      const form = await this.frService.findOne({ id });
      if (!form) {
        throw new NotFoundException();
      }
      const data = await this.frService.saveFormData(body, res, form);
      return res.status(200).json({
        status: true,
        ...data,
      });
    } catch (error) {
      return res
        .status(error?.status || HttpStatus.INTERNAL_SERVER_ERROR)
        .json({
          message:
            error?.message || 'Something went wrong. Please try again later.',
        });
    }
  }

  // @Put('update/:id')
  @Post('update/:id')
  // @UseGuards(AuthGuard('jwt'))
  async updateFormValues(@Body() body, @Param('id') id: number, @Res() res) {
    try {
      return this.frService.saveFormData(body, res, null, id);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('values/:id')
  @UseGuards(AuthGuard('jwt'))
  formValuesWithForm(@Param('id') id: number) {
    return this.frService.getFormDataWithForm(id);
  }

  @Get('check-folder/:client_id')
  @UseGuards(AuthGuard('jwt'))
  async checkFolder(
    @Param('client_id') client_id: string,
    @Req() request,
    @Res() response: Response,
    @Query('path') path?: string,
  ) {
    try {
      const client_details = await this.clientsService.findOne({
        client_id: client_id,
      });

      if (!client_details) {
        throw new NotFoundException('Client details not found');
      }

      const organization_id = request.user['isOrganization']
        ? request?.user['organization_id']
        : request?.user['organization']['organization_id'];

      let folder_name: string;

      if (path) {
        folder_name = path;
      } else if (
        client_details?.storage_folder_name &&
        client_details?.storage_folder_name != ''
      ) {
        folder_name = client_details?.storage_folder_name;
      } else {
        folder_name = client_details?.name?.trim();
      }

      const res = await this.configurationsService.checkFolder(
        organization_id,
        folder_name,
      );

      return response.send({
        ...res,
        is_existed_client: client_details?.storage_folder_name ? true : false,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('pdf/:client_id')
  @UseGuards(AuthGuard('jwt'))
  async printClientFormWithValues(
    @Param('client_id') client_id: string,
    @Body() body: any,
    @Req() request: Request,
    @Res() response: Response,
  ) {
    try {
      if (!body?.request_type) {
        throw new HttpException(
          'Request type is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      let organization_id: string;
      let app_id: string;

      if (request.user['isOrganization']) {
        organization_id = request?.user['organization_id'];
        app_id = body?.app_id;
      } else {
        organization_id = request.user['organization']['organization_id'];
        app_id = request?.user['app_id'];
      }

      return await this.orgFormsService.printClientFormWithValues(
        organization_id,
        app_id,
        client_id,
        body,
        response,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update the field in the form using form_id
   * @param form_id string
   * @param body UpdateField
   * @param response Response
   * @returns Promise<Respone>
   */
  @Patch('/custom-validations/:form_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  @UsePipes(ValidationPipe)
  async customValidations(
    @Param('form_id') form_id: string,
    @Req() request: Request,
    @Body() body: CustomValidationBody,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const user = request.user;
      let data;
      if (user['isOrganization']) {
        data = await this.orgFormsService.customValidation(
          user['organization_id'],
          form_id,
          body,
        );
      } else {
        data = await this.frService.customValidation(form_id, body);
      }
      return response.send({ status: true, data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

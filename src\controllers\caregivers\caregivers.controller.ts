import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  UseGuards,
  InternalServerErrorException,
  NotFoundException,
  Res,
  HttpStatus,
  HttpException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { CaregiversService } from '../../services/caregivers.service';
import {
  CreateCaregiverDto,
  UpdateCaregiverDto,
} from '../../dto/caregivers.dto';
import { AuthGuard } from '@nestjs/passport';
import { FindOptionsWhere } from 'typeorm';
import { Caregivers } from 'src/entities/caregivers.entity';
import { Organization } from 'src/entities/organization.entity';
import { OrganizationService } from 'src/services/organization.service';

@UseGuards(AuthGuard('jwt'))
@Controller('caregivers')
export class CaregiversController {
  constructor(
    private readonly caregiversService: CaregiversService,
    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Create a new caregiver under the logged-in user's organization
   * @param dto CreateCaregiverDto
   * @param req Request with user object
   * @returns Created caregiver
   */
  @Post()
  @UsePipes(ValidationPipe)
  async create(
    @Body() createCaregiverDto: CreateCaregiverDto,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: Caregivers = await this.caregiversService.create(
        createCaregiverDto,
        request?.user['organization_id'],
      );

      return response.status(HttpStatus.CREATED).send({
        status: true,
        message: 'Caregiver created successfully .',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get all caregivers of the logged-in user's organization
   * @param req Request with user object
   * @returns List of caregivers
   */
  @Get()
  async findAll(@Req() request: Request, @Res() response: Response) {
    try {
      let findBy: FindOptionsWhere<Caregivers> = {};

      if (
        request?.user?.['isOrganization'] &&
        request?.user?.['organization_id']
      ) {
        findBy.organization = await this.organizationService.findOne(
          { organization_id: request?.user?.['organization_id'] },
          false,
          { id: true },
        );
      }

      return response.status(HttpStatus.OK).send({
        status: true,
        message: 'Caregivers fetched successfully .',
        data: await this.caregiversService.findAll(findBy),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get caregiver by ID (only from logged-in user's organization)
   * @param id Caregiver ID
   * @param req Request with user object
   * @returns Caregiver details
   */
  @Get(':caregiver_id')
  async findOne(
    @Param('caregiver_id') caregiver_id: string,
    @Res() response: Response,
  ) {
    try {
      return response.status(HttpStatus.OK).send({
        status: true,
        message: 'Caregiver fetched successfully .',
        data: await this.caregiversService.findOne({ caregiver_id }),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update caregiver by ID (organization scoped)
   * @param caregiver_id Caregiver ID
   * @param dto Update DTO
   * @param req Request with user object
   * @returns Updated caregiver
   */
  @Patch(':caregiver_id')
  @UsePipes(ValidationPipe)
  async update(
    @Param('caregiver_id') caregiver_id: string,
    @Body() dto: UpdateCaregiverDto,
    @Req() req: any,
    @Res() response: Response,
  ) {
    try {
      return response.status(HttpStatus.OK).send({
        status: true,
        message: 'OnboardingEmployee updated successfully .',
        data: await this.caregiversService.update(caregiver_id, dto),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Soft delete caregiver by ID (organization scoped)
   * @param id Caregiver ID
   * @param req Request with user object
   * @returns void
   */
  @Delete(':caregiver_id')
  async remove(@Param('caregiver_id') caregiver_id: string, @Res() res: any) {
    try {
      await this.caregiversService.remove(caregiver_id);

      return res.send({
        status: true,
        message: 'Caregiver has been deleted successfully',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import {
  BUTTON_STYLE,
  MOBILE_LAYOUT,
  SECTION_VIEW_TYPE,
  TABLET_LAYOUT,
  THEME,
} from 'src/util/interfaces/theme.interface';

export class ThemesDto {
  @IsNotEmpty()
  name: string;

  @IsObject()
  @Type(() => Theme)
  @ValidateNested({ each: true })
  theme: THEME;
}

class Theme implements THEME {
  @IsNotEmpty()
  bodyFont: string;

  @IsNotEmpty()
  titleFont: string;

  @IsNotEmpty()
  linkColor: string;

  @IsNotEmpty()
  textColor: string;

  @IsNotEmpty()
  buttonColor: string;

  @IsNotEmpty()
  backgroundColor: string;

  @IsNotEmpty()
  buttonTextColor: string;

  @IsNotEmpty()
  navigationColor: string;

  @IsNotEmpty()
  @IsEnum(BUTTON_STYLE)
  buttonStyle: BUTTON_STYLE;

  @IsNotEmpty()
  navigationTextColor: string;

  @IsNotEmpty()
  @IsEnum(SECTION_VIEW_TYPE)
  sectionViewType: SECTION_VIEW_TYPE;

  @IsOptional()
  @IsEnum(MOBILE_LAYOUT)
  mobileLayout: MOBILE_LAYOUT;

  @IsNotEmpty()
  @IsEnum(TABLET_LAYOUT)
  tabletLayout: TABLET_LAYOUT;
}

import {
  Controller,
  Get,
  Post,
  HttpException,
  HttpStatus,
  Res,
  Body,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Param,
  Put,
  Delete,
  Req,
  Query,
} from '@nestjs/common';
import { FindOptionsWhere, Like } from 'typeorm';
import { Response, Request } from 'express';
import { AuthGuard } from '@nestjs/passport';

import { AppsDto } from 'src/dto/apps.dto';
import { Apps } from 'src/entities/apps.entity';
import { AdminAuth } from 'src/guards/admin.guard';
import { AppsService } from 'src/services/apps.service';
import { AdminOrOrganizationAuth } from 'src/guards/adminOrOrganization.guard';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { OrganizationService } from 'src/services/organization.service';

@Controller('apps')
export class AppsController {
  constructor(
    private AppsService: AppsService,
    private readonly industryTypesService: IndustryTypesService,
    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Get all apps with out authorization
   * @param response Response
   * @returns Promise<Response>
   */
  @Get()
  async getApps(
    @Res() response: Response,
    @Query('industry_type_id') industry_type_id?: string,
    @Query('name') name?: string,
  ): Promise<Response> {
    try {
      const condition: FindOptionsWhere<Apps> = {};

      if (industry_type_id) {
        const industry_types = await this.industryTypesService.findOne(
          { industry_type_id },
          false,
          { id: true },
        );
        condition.industry_types = industry_types;
      }

      if (name) {
        condition.name = Like(`%${name}%`);
      }

      return response.send({
        status: true,
        message: 'Apps list fetched successfully.',
        data: await this.AppsService.list(condition, null, false, {
          industry_app_process: true,
        }),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get all apps based on authorization
   * @param response Response
   * @param request Request
   * @returns Promise<Response>
   */
  @Get('/me')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getMyApps(
    @Res() response: Response,
    @Req() request: Request,
  ): Promise<Response> {
    try {
      let organization_id: number;

      const isOrganization = request.user['isOrganization']
        ? request?.user['organization_id']
        : '';

      const findBy: FindOptionsWhere<Apps> = {};

      if (isOrganization) {
        const organization = await this.AppsService.getOrganization(
          isOrganization,
        );
        findBy.organizations = organization;

        organization_id = organization.id;
      }

      let data: Apps[] = await this.AppsService.list(findBy, null, false, {
        industry_app_process: true,
      });

      return response.send({
        status: true,
        message: 'Success',
        data,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Create App
   * @param body AppsDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Post()
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), AdminAuth)
  async createApp(
    @Body() body: AppsDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: Boolean = await this.AppsService.insert(body);

      return data
        ? response.status(HttpStatus.CREATED).send({
            status: true,
            message: 'App created successfully.',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to create the App.',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get App Details with public access
   * @param app_code string
   * @param organization_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/public')
  async getAppDetailsWithPublicAccess(
    @Query('app_code') app_code: string,
    @Query('organization_id') organization_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const findBy: FindOptionsWhere<Apps> = { app_code };

      if (organization_id) {
        const organization = await this.AppsService.getOrganization(
          organization_id,
        );
        findBy.organizations = organization;
      }

      const data = await this.AppsService.findOne(findBy);

      return data
        ? response.send({ status: true, message: 'App get successfully', data })
        : response
            .status(HttpStatus.NOT_FOUND)
            .send({ status: false, message: 'App Not Found' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get forms by app id
   * @parma app_id string
   * @return Promise<Response>
   */
  @Get('/forms/:app_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getFormsByApp(
    @Param('app_id') app_id: string,
    @Query('organization') organization: string,
    @Res() response: Response,
    @Req() req: Request,
  ): Promise<Response> {
    try {
      const isOrganization = req.user['isOrganization']
        ? req?.user['organization_id']
        : '';
      const data = await this.AppsService.getFormsById(
        app_id,
        isOrganization,
        organization,
      );
      return data
        ? response.send({ status: true, message: 'App get successfully', data })
        : response
            .status(HttpStatus.NOT_FOUND)
            .send({ status: false, message: 'App Not Found' });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get app by the app_id
   * @param app_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/:app_id')
  @UseGuards(AuthGuard('jwt'), AdminOrOrganizationAuth)
  async getAppById(
    @Param('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const findBy: FindOptionsWhere<Apps> = {};

      const isOrganization = request.user['isOrganization']
        ? request?.user['organization_id']
        : '';

      findBy.app_id = app_id;

      if (isOrganization) {
        const organization = await this.AppsService.getOrganization(
          isOrganization,
        );
        findBy.organizations = organization;
      }

      const data = await this.AppsService.getById(findBy);
      return data
        ? response.send({ status: true, message: 'App get successfully', data })
        : response
            .status(HttpStatus.NOT_FOUND)
            .send({ status: false, message: 'App Not Found' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/:app_id')
  @UseGuards(AuthGuard('jwt'), AdminAuth)
  async updateByAppId(
    @Param('app_id') app_id: string,
    @Body() body: AppsDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: boolean = await this.AppsService.update(app_id, body);
      return data
        ? response.send({ status: true, message: 'App updated successfully' })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to update the App' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('/:app_id')
  @UseGuards(AuthGuard('jwt'), AdminAuth)
  async deleteAppById(
    @Param('app_id') app_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data = await this.AppsService.delete(app_id);
      return data
        ? response.send({ status: true, message: 'App deleted successfully' })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to delete App' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

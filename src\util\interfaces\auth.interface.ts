import { Admin } from 'src/entities/admin.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Organization } from 'src/entities/organization.entity';
import { User } from 'src/entities/user.entity';

export interface AUTH_TOKEN {
  access_token: string;
  token_type: string;
}

export type USER = User;

export interface ADMIN extends Admin {
  isAdmin: boolean;
}
export interface ORGANIZATION extends Organization {
  isOrganization: boolean;
}

export interface EMPLOYEE extends OnboardingEmployee {
  isOnboardingEmployee: boolean;
}

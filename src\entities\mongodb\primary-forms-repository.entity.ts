import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  HydratedDocument,
  ObjectId,
  SchemaOptions,
  SchemaTypes,
} from 'mongoose';
import { OrgFormsRepository } from './orgformsrepository.entity';
import { FormValuesRepository } from './formvaluesrepository.entity';

const PrimaryFormsRepositorySchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'primary_form_repository',
  versionKey: false,
};

@Schema(PrimaryFormsRepositorySchemaOptions)
export class PrimaryFormsRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ unique: true, type: SchemaTypes.String, required: true })
  primary_form_id: string;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'OrgFormsRepository',
    required: true,
  })
  original_form: OrgFormsRepository;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'FormValuesRepository',
  })
  values: FormValuesRepository;

  @Prop({ type: SchemaTypes.String, required: true })
  organization_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  app_id: string;

  @Prop({ default: true, type: SchemaTypes.Boolean })
  status: boolean;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type PrimaryFormsRepositoryDocument =
  HydratedDocument<PrimaryFormsRepository>;

export const PrimaryFormsRepositorySchema = SchemaFactory.createForClass(
  PrimaryFormsRepository,
);

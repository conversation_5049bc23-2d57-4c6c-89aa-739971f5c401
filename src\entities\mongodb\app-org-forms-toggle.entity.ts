import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import {
  SchemaOptions,
  SchemaTypes,
  ObjectId,
  HydratedDocument,
} from 'mongoose';

const appOrgFormsToggleRepositorySchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'app_org_forms_toggle_repository',
  versionKey: false,
};

@Schema(appOrgFormsToggleRepositorySchemaOptions)
export class AppOrgFormsToggleRepository {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ type: SchemaTypes.String, required: true })
  organization_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  app_id: string;

  @Prop({ type: SchemaTypes.String, required: false })
  form_id: string;

  @Prop({ default: true, type: SchemaTypes.Boolean })
  status: boolean;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type AppOrgFormsToggleRepositoryDocument =
  HydratedDocument<AppOrgFormsToggleRepository>;

export const AppOrgFormsToggleRepositorySchema = SchemaFactory.createForClass(
  AppOrgFormsToggleRepository,
);

import {
  Controller,
  HttpException,
  HttpStatus,
  Param,
  Res,
  Get,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response, Request } from 'express';
import CountryStateCityService from 'src/services/country-state-city.service';

@Controller('country-state-city')
export default class CountryStateCityController {
  constructor(
    private readonly countryStateCityService: CountryStateCityService,
  ) {}

  /**
   * Fetch Address details with latitude and longitude
   * @returns Promise<Response>
   */
  @Get('county-code')
  @UseGuards(AuthGuard('jwt'))
  async addressDetails(
    @Req() request: Request,
    @Res() response: Response,
    @Query('latitude') latitude: string,
    @Query('longitude') longitude: string,
  ): Promise<Response> {
    try {
      const organization_id = request?.user['organization']['organization_id'];

      const data = await this.countryStateCityService.addressDetailsWithLatLong(
        organization_id,
        latitude,
        longitude,
      );

      return data
        ? response.status(200).send({
            status: true,
            data: data,
            message: 'Data fetched Successfully',
          })
        : response
            .status(HttpStatus.BAD_REQUEST)
            .send({ status: false, message: 'Bad Request.', data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Fetch the countries list
   * @returns any
   */
  @Get('countries')
  async countriesList(@Res() response: Response): Promise<Response> {
    try {
      const data = await this.countryStateCityService.countriesList();

      return data
        ? response.status(200).send({
            status: true,
            data: data,
            message: 'Data Saved Successfully',
          })
        : response
            .status(HttpStatus.BAD_REQUEST)
            .send({ status: false, message: 'Bad Request.', data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Fetch the states list with countyCode
   * @param countryCode:string
   * @returns any
   */
  @Get('states/:countryCode')
  async statesListByCountry(
    @Res() response: Response,
    @Param('countryCode') countryCode: string,
  ): Promise<Response> {
    try {
      const data = await this.countryStateCityService.statesListByCountry(
        countryCode,
      );

      return data
        ? response.status(200).send({
            status: true,
            data: data,
            message: 'Data Saved Successfully',
          })
        : response
            .status(HttpStatus.BAD_REQUEST)
            .send({ status: false, message: 'Bad Request.', data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Fetch the cities list with countyCode and stateCode
   * @param countryCode:string
   * @param stateCode:string
   * @returns any
   */
  @Get('cities/:countryCode/:stateCode')
  async citiesListByCountry(
    @Res() response: Response,
    @Param('countryCode') countryCode: string,
    @Param('stateCode') stateCode: string,
  ): Promise<Response> {
    try {
      const data = await this.countryStateCityService.citiesListByCountry(
        countryCode,
        stateCode,
      );

      return data
        ? response.status(200).send({
            status: true,
            data: data,
            message: 'Data Saved Successfully',
          })
        : response
            .status(HttpStatus.BAD_REQUEST)
            .send({ status: false, message: 'Bad Request.', data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

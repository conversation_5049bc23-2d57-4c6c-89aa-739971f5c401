import {
  IsEmail,
  IsNotEmpty,
  Is<PERSON>tring,
  <PERSON>A<PERSON>y,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { ClientsExists } from 'src/util/validations/caregiver.validations';

export class CreateCaregiverDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  @Matches(/^[\+]?[1-9][\d]{0,15}$/, {
    message: 'Mobile number must be a valid phone number',
  })
  mobile_number: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(6, { message: 'Password must be at least 8 characters long' })
  password: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ClientsExists({ each: true })
  clients?: string[];
}

export class UpdateCaregiverDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  @Matches(/^[\+]?[1-9][\d]{0,15}$/, {
    message: 'Mobile number must be a valid phone number',
  })
  mobile_number?: string;

  @IsOptional()
  @IsString()
  @MinLength(6, { message: 'Password must be at least 8 characters long' })
  password?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ClientsExists({ each: true })
  clients?: string[];
}

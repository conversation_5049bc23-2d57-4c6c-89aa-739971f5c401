import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { AppNameExists } from 'src/util/validations/apps.validation';
import { IndustryAppProcessExists } from 'src/util/validations/industry-app-process.validation';
import { IndustryTypesExists } from 'src/util/validations/industry-types.validation';

export class AppsDto {
  @IsNotEmpty()
  @AppNameExists()
  name: string;
  @IsOptional()
  description: string;

  @IndustryTypesExists()
  @IsNotEmpty()
  @IsString()
  industry_type_id: string;

  @IndustryAppProcessExists()
  @IsNotEmpty()
  @IsString()
  industry_app_process_id: string;
}

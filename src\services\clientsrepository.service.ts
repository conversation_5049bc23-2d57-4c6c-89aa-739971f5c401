import {
  Body,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { generateUUID } from 'src/util';
import { FormFields } from 'src/entities/mongodb/formfields.entity';
import {
  ClientsRepository,
  ClientsRepositoryDocument,
} from 'src/entities/mongodb/clientsrepository.entity';
import { CreateClientRepositoryDto } from 'src/dto/clientsrepository.dto';
import { AppsService } from './apps.service';

@Injectable()
export class ClientsRepositoryService {
  constructor(
    @InjectModel(ClientsRepository.name)
    private readonly ClientsRepository: Model<ClientsRepositoryDocument>,
    private readonly appsService: AppsService,
  ) {}

  /**
   * @variable select
   */

  private select: QueryOptions<ClientsRepository> = {
    _id: false,
    name: true,
    mobile_number: true,
    user_id: true,
    address: true,
    form_values: true,
    storage_folder_name: true,
    client_id: true,
    createdAt: true,
    updatedAt: true,
    deleted_at: true,
  };

  /**
   * Create Client
   * @param data CreateClientRepositoryDto
   * @param user any
   * @returns Promise<ClientsRepository>
   */
  async create(
    data: CreateClientRepositoryDto,
    user: any,
    app_code: any,
  ): Promise<ClientsRepository> {
    try {
      if (!app_code) {
        throw new HttpException('App code is required', HttpStatus.BAD_REQUEST);
      }

      const appDetails = await this.appsService.findOne(
        { app_code },
        false,
        null,
        {
          industry_app_process: true,
        },
      );

      if (!appDetails) {
        throw new NotFoundException('App not found');
      }

      if (
        appDetails &&
        appDetails?.industry_app_process &&
        appDetails?.industry_app_process?.process_code == 'HC_CLIASS'
      ) {
        return this.ClientsRepository.create({
          ...data,
          user_id: user['user_id'],
          app_id: user['app_id'],
          organization_id: user['organization'],
          client_id: generateUUID(),
        });
      } else {
        throw new HttpException(
          'App code is not valid',
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (error) {
      throw error;
    }
  }

  async checkClientRequiredOrNot(app_code: any) {
    if (!app_code) {
      throw new HttpException('App code is required', HttpStatus.BAD_REQUEST);
    }

    const appDetails = await this.appsService.findOne(
      { app_code },
      false,
      null,
      {
        industry_app_process: true,
      },
    );

    if (!appDetails) {
      throw new NotFoundException('App not found');
    }

    if (
      appDetails &&
      appDetails?.industry_app_process &&
      appDetails?.industry_app_process?.process_code == 'HC_CLIASS'
    ) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * Update the Client by client_id
   * @param data CreateClientRepositoryDto.
   * @param client_id string
   * @param user any
   * @returns Promise<ClientsRepository>
   */
  async update(
    client_id: string,
    data: CreateClientRepositoryDto,
  ): Promise<ClientsRepository> {
    try {
      const form = await this.findOne({ client_id });

      if (!form) {
        throw new NotFoundException();
      }

      return await this.ClientsRepository.findOneAndUpdate(
        {
          client_id: form.client_id,
        },
        data,
        { returnDocument: 'after' },
      ).select(this.select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find client record by condition
   * @param condition Object "{column: value}"
   * @returns Promise<ClientsRepository>
   */
  async findOne(
    condition: QueryOptions<ClientsRepository>,
    withDeleted = false,
    select: QueryOptions<ClientsRepository> = this.select,
  ): Promise<ClientsRepository> {
    try {
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }
      return await this.ClientsRepository.findOne(condition).select(select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get All Form Clients
   * @returns Promise<ClientsRepository[]>
   */
  async findAll(
    condition: QueryOptions<ClientsRepository> = { status: true },
    withDeleted = false,
    select: QueryOptions<ClientsRepository> = this.select,
    order: any = { createdAt: -1 },
  ): Promise<ClientsRepository[]> {
    if (!condition) condition = { status: true };

    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    } else {
      condition['deleted_at'] = { $eq: null };
    }
    if (!select) select = this.select;

    const list = await this.ClientsRepository.find(condition)
      .select(select)
      .sort(order);

    return list;
  }

  /**
   * Delete form client records by condition
   * @returns Promise<ClientsRepository>
   */
  async delete(client_id: string): Promise<ClientsRepository> {
    try {
      const client = await this.findOne({ client_id });

      if (!client) {
        throw new NotFoundException();
      }

      return await this.ClientsRepository.findOneAndUpdate(
        { client_id },
        {
          deleted_at: Date.now(),
        },
        {
          new: true,
        },
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

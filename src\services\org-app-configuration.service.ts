import { HttpStatus, HttpException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OrgAppConfigurationDto } from 'src/dto/org-app-configuration.dto';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Organization } from 'src/entities/organization.entity';
import { generateUUID } from 'src/util';
import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  Not,
  Repository,
} from 'typeorm';
import { AppsService } from './apps.service';
import { OrganizationService } from './organization.service';

@Injectable()
export class OrgAppConfigurationService {
  constructor(
    @InjectRepository(OrgAppConfiguration, 'mysql')
    private orgAppConfigurationRepository: Repository<OrgAppConfiguration>,
    private readonly appService: AppsService,
    private readonly organizationService: OrganizationService,
  ) {}

  public select: FindOptionsSelect<OrgAppConfiguration> = {
    id: false,
    org_app_configuration_id: true,
    app_name: true,
    description: true,
    status: true,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create Organization App Configuration
   * @param orgAppConfigurationDto OrgAppConfigurationDto
   * @returns Promise<OrgAppConfiguration>
   */

  async create(
    orgAppConfigurationDto: OrgAppConfigurationDto,
    app_id: string,
    organization_id: any,
  ): Promise<OrgAppConfiguration> {
    try {
      const uuid = generateUUID();

      const app = await this.appService.findOne({ app_id }, false, {
        id: true,
      });

      if (!app) {
        throw new HttpException('App not found', HttpStatus.NOT_FOUND);
      }

      const organization = await this.organizationService.findOne(
        {
          organization_id,
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      // Check if the custom app name is unique within the organization
      const existingConfig = await this.orgAppConfigurationRepository.findOne({
        where: {
          app_name: orgAppConfigurationDto.app_name,
          organization: organization,
        },
      });

      if (existingConfig) {
        throw new HttpException(
          'App name already exists within the organization',
          HttpStatus.CONFLICT,
        );
      }

      return await this.orgAppConfigurationRepository.save({
        ...orgAppConfigurationDto,
        org_app_configuration_id: uuid,
        app,
        organization,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get One record by the where condition
   * @param where FindOptionsWhere<OrgAppConfigurationDto>
   * @param withDeleted boolean
   * @param select FindOptionsSelect<OrgAppConfigurationDto>
   * @returns Promise<OrgAppConfigurationDto>
   */
  async findOne(
    where: FindOptionsWhere<OrgAppConfiguration>,
    withDeleted: boolean = false,
    select: FindOptionsSelect<OrgAppConfiguration> = this.select,
    relations: FindOptionsRelations<OrgAppConfiguration> = {},
  ): Promise<OrgAppConfiguration> {
    try {
      return await this.orgAppConfigurationRepository.findOne({
        where,
        withDeleted,
        select,
        relations,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update Organization App Configuration
   * @param OrgAppConfigurationDto OrgAppConfigurationDto
   * @returns Promise<OrgAppConfiguration>
   */

  async update(
    org_app_configuration_id: string,
    orgAppConfigurationDto: OrgAppConfigurationDto,
  ): Promise<OrgAppConfiguration> {
    try {
      const data: OrgAppConfiguration = await this.findOne(
        { org_app_configuration_id },
        false,
        {
          ...this.select,
          id: true,
        },
      );

      if (!data) {
        throw new HttpException(
          'Org app configuration Not Found',
          HttpStatus.NOT_FOUND,
        );
      }

      // Check if the custom app name is unique within the organization
      const existingConfig = await this.orgAppConfigurationRepository.findOne({
        where: {
          app_name: orgAppConfigurationDto.app_name,
          organization: data.organization,
          org_app_configuration_id: Not(org_app_configuration_id), // Exclude the current configuration
        },
      });

      if (existingConfig) {
        throw new HttpException(
          'App name already exists within the organization',
          HttpStatus.CONFLICT,
        );
      }

      return await this.orgAppConfigurationRepository.save({
        id: data.id,
        ...orgAppConfigurationDto,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  // async findOne(
  //   findBy: FindOptionsWhere<IndustryAppProcess>,
  //   withDeleted = false,
  //   select = this.select,
  // ): Promise<IndustryAppProcess> {
  //   try {
  //     return await this.IndustryAppProcessRepository.findOne({
  //       where: findBy,
  //       withDeleted,
  //       select,
  //     });
  //   } catch (error) {
  //     throw new HttpException(
  //       error?.message || 'Something went wrong. Please try again later.',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // /**
  //  * Find Industry Types
  //  * @param findBy Object "{column: value}"
  //  * @returns Promise<IndustryTypes[]>
  //  */

  // async find(
  //   findBy: FindOptionsWhere<IndustryAppProcess>,
  //   withDeleted = false,
  //   select = this.select,
  // ): Promise<IndustryAppProcess[]> {
  //   try {
  //     return await this.IndustryAppProcessRepository.find({
  //       where: findBy,
  //       withDeleted,
  //       relations: {
  //         apps: true,
  //         industry_types: true,
  //       },
  //       select,
  //     });
  //   } catch (error) {
  //     throw new HttpException(
  //       error?.message || 'Something went wrong. Please try again later.',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }
}

import {
  IsNotEmpty,
  IsEmail,
  IsOptional,
  IsArray,
  ArrayNotEmpty,
  ArrayUnique,
  IsString,
  IsBoolean,
  ValidateIf,
} from 'class-validator';
import { AppsExists } from 'src/util/validations/apps.validation';
import {
  FormsExists,
  OrgFormsExists,
} from 'src/util/validations/forms.validations';
import { IndustryTypesExists } from 'src/util/validations/industry-types.validation';
import {
  OrganizationMailExists,
  OrganizationMobileExists,
  OrganizationNameExists,
} from 'src/util/validations/organization.validation';

export class OrganizationRegisterDto {
  @IsNotEmpty()
  @OrganizationNameExists()
  name: string;
  @IsNotEmpty()
  password: string;

  @IsOptional()
  logo: string;

  @IsNotEmpty()
  @IsEmail()
  @OrganizationMailExists()
  email: string;

  @IsString()
  @IsNotEmpty()
  @OrganizationMobileExists()
  mobile_number: string;
  // @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @AppsExists()
  apps: string[];

  @IndustryTypesExists()
  @IsNotEmpty()
  @IsString()
  industry_type_id: string;
}

export class OrganizationUpdateDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  password: string;

  @IsOptional()
  logo: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsString()
  mobile_number: string;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @AppsExists()
  apps: string[];

  @IsOptional()
  // @IsBoolean()
  // @IsNotEmpty()
  check_org_apps_assigned_to_user?: boolean;

  @IsOptional()
  // @ValidateIf((o) => o.check_org_apps_assigned_to_user === true)
  // @IsNotEmpty()
  // @IsBoolean()
  confirm_remove_user_apps?: boolean;

  @IsOptional()
  @IsString()
  pass_key?: string;
}

export class AddFormsOrganization {
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @FormsExists()
  form_ids: string[];
}

export class RemoveFormOrganization {
  @IsNotEmpty()
  // @FormsExists()
  form_id: string;
}
export class AddFormOrganization {
  @IsNotEmpty()
  // @FormsExists()
  form_id: string;
}
export class UpdateFormsStatusDto {
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @OrgFormsExists()
  form_ids: string[];
  @IsNotEmpty()
  @IsBoolean()
  status: boolean;
}

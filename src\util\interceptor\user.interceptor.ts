import {
  CallHand<PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  UseInterceptors,
  applyDecorators,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import * as _ from 'lodash';
import { REQUEST_CONTEXT } from '../constants';

@Injectable()
class InjectUserInterceptor implements NestInterceptor {
  constructor(private type?: 'query' | 'body' | 'params') {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    if (this.type && request[this.type]) {
      request[this.type][REQUEST_CONTEXT] = {
        ...request[this.type][REQUEST_CONTEXT],
        user: request.user,
      };
    }

    return next.handle();
  }
}

/* @Injectable()
export class StripRequestContextPipe implements PipeTransform {
  transform(value: any) {
    return _.omit(value, REQUEST_CONTEXT);
  }
}*/

export function InjectUserToQuery() {
  return applyDecorators(InjectUserTo('query'));
}

export function InjectUserToBody() {
  return applyDecorators(InjectUserTo('body'));
}

export function InjectUserToParam() {
  return applyDecorators(InjectUserTo('params'));
}

export function InjectUserTo(context: 'query' | 'body' | 'params') {
  return applyDecorators(
    UseInterceptors(new InjectUserInterceptor(context)),
    // UsePipes(StripRequestContextPipe),
  );
}

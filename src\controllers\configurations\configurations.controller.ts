import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Req,
  Res,
  UseGuards,
  Patch,
  Param,
  Get,
  Query,
  Delete,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request, Response, request, response } from 'express';
import { stat } from 'fs';
import { ConfigurationDto } from 'src/dto/configuration.dto';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { ConfigurationsService } from 'src/services/configurations.service';

@Controller('configurations')
export default class ConfigurationsController {
  constructor(private configurationsService: ConfigurationsService) {}

  /**
   * Create Configuration Details
   * @param configurationDto ConfigurationDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Post()
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async create(
    @Req() request: Request,
    @Res() response: Response,
    @Body() configurationDto: ConfigurationDto,
  ): Promise<Response> {
    try {
      configurationDto.organization_id = request?.user['organization_id'];

      const data = await this.configurationsService.create(configurationDto);

      return data
        ? response.status(201).send({
            status: true,
            data: data,
            message: 'Configuration created Successfully',
          })
        : response
            .status(HttpStatus.BAD_REQUEST)
            .send({ status: false, message: 'Bad Request.', data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update Configuration Details
   * @param configurationDto ConfigurationDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('/:configuration_id')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async update(
    @Param('configuration_id') configuration_id: string,
    @Req() request: Request,
    @Res() response: Response,
    @Body() configurationDto: ConfigurationDto,
  ): Promise<Response> {
    try {
      const organization_id = request?.user['organization_id'];
      const data = await this.configurationsService.update(
        configuration_id,
        configurationDto,
        organization_id,
      );

      return data
        ? response.status(200).send({
            status: true,
            data: data,
            message: 'Configuration updated Successfully',
          })
        : response
            .status(HttpStatus.BAD_REQUEST)
            .send({ status: false, message: 'Bad Request.', data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/login-link')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async generateOneDriveLink(
    @Req() request: Request,
    @Res() response: Response,
  ) {
    try {
      const organizationId = request?.user['organization_id'];

      const loginLink = await this.configurationsService.generateLoginLink(
        organizationId,
      );

      return response.status(200).send({
        status: true,
        data: loginLink,
        message: 'Login link details fetched Successfully',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/redirect/:organization_id')
  async getAuthToken(
    @Res() response: Response,
    @Param('organization_id') organization_id: string,
    @Query('code') code: string,
    @Query('message') message?: string,
  ) {
    try {
      const getAuthTokenStatus = await this.configurationsService.getAuthToken(
        organization_id,
        code,
        message,
      );

      return response.status(200).send({
        status: true,
        message: 'Authentication was successfully completed.',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/one-drive/refresh')
  getOneDriveRefreshToken(@Query('organization_id') organization_id: string) {
    return this.configurationsService.getOneDriveRefreshToken(organization_id);
  }

  @Get('folders')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  getDriverFolders(@Req() request: Request) {
    const organizationId = request?.user['organization_id'];
    return this.configurationsService.getFoldersList(organizationId);
  }

  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  @Post('google-drive/folder/create')
  async createFolder(@Body() body: any, @Req() request: Request) {
    const organizationId = request?.user['organization_id'];
    return (
      await this.configurationsService.createFolder(organizationId, body?.name)
    ).data;
  }

  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  @Delete('google-drive/folder/:id')
  async deleteFolder(@Param('id') id: string, @Req() request: Request) {
    const organizationId = request?.user['organization_id'];

    return this.configurationsService.deleteFolder(organizationId, id);
  }

  /**
   * Fetch The Configuration Details By Id
   * @param configurationDto ConfigurationDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Get()
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async findAll(
    @Query('type') type: string,
    @Req() request: Request,
    @Res() response: Response,
    @Query('configuration_id') configuration_id?: string,
  ): Promise<Response> {
    try {
      const organization_id = request?.user['organization_id'];

      const organization = await this.configurationsService.getOrganization(
        organization_id,
      );

      const data = await this.configurationsService.findOne({
        organization,
        type,
      });

      return data
        ? response.status(200).send({
            status: true,
            data: data,
            message: 'Configuration details fetched Successfully',
          })
        : response
            .status(HttpStatus.BAD_REQUEST)
            .send({ status: false, message: 'Bad Request.', data });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { Injectable } from '@nestjs/common';

import { REQUEST_CONTEXT } from '../constants';
import { UserService } from 'src/services/user.service';
import { OrganizationService } from 'src/services/organization.service';
import { ExtendedValidationArguments } from '../interfaces/validation.interface';

@ValidatorConstraint({ name: 'UserMailExists', async: true })
@Injectable()
export class UserMailExistsRule implements ValidatorConstraintInterface {
  constructor(
    private userService: UserService,
    private organizationService: OrganizationService,
  ) { }

  async validate(value: string, args: ExtendedValidationArguments) {
    try {
      const organization_id =
        args.object['organization'] ||
        args.object[REQUEST_CONTEXT].user?.organization_id;
      const organization = await this.organizationService.findOne(
        {
          organization_id,
        },
        false,
        { id: true },
      );
      const user = await this.userService
        .findOne(
          {
            email: value,
            organization,
          },
          false,
          { id: true },
        )
        .catch((error) => console.log(error));
      return user ? false : true;
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  defaultMessage() {
    return `Entered Mail id was already existed`;
  }
}

export function UserMailExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'UserMailExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: UserMailExistsRule,
    });
  };
}

@ValidatorConstraint({ name: 'UserMobileExists', async: true })
@Injectable()
export class UserMobileExistsRule implements ValidatorConstraintInterface {
  constructor(
    private userService: UserService,
    private organizationService: OrganizationService,
  ) { }

  async validate(value: string, args: ExtendedValidationArguments) {
    try {
      const organization_id =
        args.object['organization'] ||
        args.object[REQUEST_CONTEXT].user?.organization_id;
      const organization = await this.organizationService.findOne(
        {
          organization_id,
        },
        false,
        { id: true },
      );
      const user = await this.userService
        .findOne(
          {
            mobile_number: value,
            organization,
          },
          false,
          { id: true },
        )
        .catch((error) => console.log(error));
      return user ? false : true;
    } catch (e) {
      return false;
    }
  }

  defaultMessage() {
    return `Entered Mobile number was already existed`;
  }
}

export function UserMobileExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'UserMobileExists',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: UserMobileExistsRule,
    });
  };
}

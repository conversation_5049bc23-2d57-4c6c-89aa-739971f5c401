import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthDto, updatePasswordDTO } from 'src/dto/auth.dto';
import { User as UserEntity } from 'src/entities/user.entity';
import AuthService from 'src/services/auth.service';
import { User } from 'src/util/decorator/custom.decorator';
import { Response } from 'express';
import { EMPLOYEE } from 'src/util/interfaces/auth.interface';
import { Apps } from 'src/entities/apps.entity';
import { AppsService } from 'src/services/apps.service';
import { OrgAppConfigurationService } from 'src/services/org-app-configuration.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OrganizationService } from 'src/services/organization.service';
import { UserOrOrganizationAuth } from 'src/guards/userOrOrganization.guard';

@Controller('auth')
export default class AuthController {
  constructor(
    private authService: AuthService,
    private appsService: AppsService,
    private readonly OrgAppConfigurationService: OrgAppConfigurationService,
    private readonly onboardingEmployeeService: OnboardingEmployeeService,
    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Authenticate the user
   * @param user User
   */
  @UseGuards(AuthGuard('local'))
  @Post('/login')
  login(@User() user: UserEntity | EMPLOYEE) {
    return this.authService.generateToken(user);
  }

  /**
   * Validate the logged in user
   * @param user User
   */
  @UseGuards(AuthGuard('jwt'), UserOrOrganizationAuth)
  @Get('/validate-user')
  async validateUser(@Req() req: any) {
    let user: any = req.user;

    if (user?.isOrganization) {
      return user;
    }

    const app: Apps = await this.appsService.findOne(
      { app_code: req.headers['kleza-app-code'] },
      false,
      { id: true },
      { industry_app_process: true },
    );

    if (!app) {
      throw new HttpException('Invalid App', HttpStatus.BAD_REQUEST);
    }

    const onboardingApps = ['HC_OBRD'];

    let organization: any = user.organization;

    let findBy: any = { organization_id: organization };

    if (user?.isOrganization) {
      findBy = { organization_id: organization.organization_id };
    }

    organization = await this.organizationService.findOne(findBy, false, {
      id: true,
    });

    if (
      onboardingApps.includes(app?.industry_app_process?.process_code) &&
      organization
    ) {
      const onboarding_employee = await this.onboardingEmployeeService.findOne({
        onboarding_employee_id: user.onboarding_employee_id,
      });

      user.status = onboarding_employee?.status || null;

      // const onboarding_employees: any =
      //   await this.onboardingEmployeeService.employeesWithFormStatus(
      //     [onboarding_employee],
      //     organization,
      //   );

      user.status = onboarding_employee?.status || null;

      user.is_rejected = onboarding_employee?.is_rejected;
    }

    if (app && organization) {
      user.orgAppConfiguration = await this.OrgAppConfigurationService.findOne(
        {
          app: { id: app.id },
          organization,
        },
        false,
        null,
        { app: true, organization: true },
      );
    }

    return user;
  }

  /**
   * Authenticate the user
   * @param login AuthDto
   */
  @Post('/organization/login')
  organizationLogin(@Body() login: AuthDto) {
    return this.authService.validateOrganization(login);
  }

  @Post('/admin/login')
  adminLogin(@Body() login: AuthDto) {
    return this.authService.validateAdmin(login);
  }

  @Post('/update-password')
  @UseGuards(AuthGuard('jwt'))
  async updatePassword(
    @Body() body: updatePasswordDTO,
    @Req() req: any,
    @Res() response: Response,
  ) {
    try {
      const data = await this.authService.updatePassword(body, req.user);

      return data
        ? response.send({
            status: true,
            message: 'Password Updated Successfully',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to update the Organization',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

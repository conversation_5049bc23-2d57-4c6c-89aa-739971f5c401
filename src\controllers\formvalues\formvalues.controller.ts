import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  NotFoundException,
  Param,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AuthGuard } from '@nestjs/passport';
import { Request, Response } from 'express';
import { Model, QueryOptions } from 'mongoose';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositoryDocument,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import { FormValuesRepository } from 'src/entities/mongodb/formvaluesrepository.entity';
import { OrgFormsRepository } from 'src/entities/mongodb/orgformsrepository.entity';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { AppsService } from 'src/services/apps.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { FormValueService } from 'src/services/formvalues.service';
import { OrganizationService } from 'src/services/organization.service';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { OrgFormsRepositoryWithObjectId } from 'src/util/interfaces/forms.interface';

@Controller('form-value')
export class FormValueController {
  constructor(
    @InjectModel(AppOrgFormsToggleRepository.name)
    private appOrgFormsToggleRepository: Model<AppOrgFormsToggleRepositoryDocument>,

    private readonly formValueService: FormValueService,

    private readonly clientsRepositoryService: ClientsRepositoryService,

    private readonly orgFormsRepositoryService: OrgFormsRepositoryService,

    private readonly appsService: AppsService,

    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Get Form Values based on app, organization, client, form
   * @param app_id string
   * @param organization_id string
   * @param client_id string
   * @param form_id string
   * @returns Promise<Response>
   */
  @Get()
  // @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async get(
    @Res() response: Response,
    @Query('app_id') app_id?: string,
    @Query('organization_id') organization_id?: string,
    @Query('client_id') client_id?: string,
    @Query('user_id') user_id?: string,
    @Query('form_id') form_id?: string,
    @Query('expand') expand?: string,
    @Query('select') select?: any,
    @Query('exportToExcel') exportToExcel?: boolean,
  ): Promise<Response> {
    try {
      const condition: QueryOptions<FormValuesRepository> = {};

      if (app_id && organization_id) {
        const app = await this.appsService.findOne({ app_id });

        if (!app) {
          throw new NotFoundException('App Not Found');
        }

        let organizationForms: OrgFormsRepositoryWithObjectId[] =
          await this.orgFormsRepositoryService.find(
            { app_id, organization: organization_id, is_sub_form: false },
            false,
            {
              _id: true,
              order_position: true,
              form_id: true,
              has_sub_forms: true,
              name: true,
            },
          );

        const toggleDetails: AppOrgFormsToggleRepository =
          await this.appOrgFormsToggleRepository.findOne({
            app_id,
            organization_id,
          });

        const processForms = async (
          forms: OrgFormsRepositoryWithObjectId[],
        ): Promise<string[]> => {
          const processFormsIds: string[] = [];

          for (const orgForm of forms) {
            if (orgForm.has_sub_forms) {
              let orgFormSubForms: OrgFormsRepositoryWithObjectId[] =
                await this.orgFormsRepositoryService.find(
                  {
                    app_id,
                    organization: organization_id,
                    main_form_id: orgForm._id,
                  },
                  false,
                  {
                    _id: true,
                    order_position: true,
                    form_id: true,
                    has_sub_forms: true,
                    name: true,
                  },
                );

              const subFormToggleDetails: AppOrgFormsToggleRepository | null =
                await this.appOrgFormsToggleRepository.findOne({
                  app_id,
                  organization_id,
                  form_id: orgForm.form_id,
                });

              if (subFormToggleDetails?.status) {
                orgFormSubForms.sort(
                  (a, b) => a.order_position - b.order_position,
                );
              }

              for (const form of orgFormSubForms) {
                processFormsIds.push(form._id);
              }
            } else {
              processFormsIds.push(orgForm._id);
            }
          }

          return processFormsIds;
        };

        if (toggleDetails && toggleDetails.status) {
          organizationForms = organizationForms.sort(
            (a, b) => a.order_position - b.order_position,
          );
        }

        condition.form = { $in: await processForms(organizationForms) };
      }

      if (client_id) {
        const client: any = await this.clientsRepositoryService.findOne(
          {
            client_id,
          },
          false,
          {
            id: true,
          },
        );

        if (!client) {
          throw new HttpException('Client not found', HttpStatus.NOT_FOUND);
        }
        condition.client = client._id;
      }

      if (user_id) {
        condition.user_id = user_id;
      }

      if (form_id) {
        const orgForm: any = await this.orgFormsRepositoryService.findOne(
          {
            form_id,
          },
          false,
          { id: true },
        );
        if (!orgForm) {
          throw new HttpException('Form not found', HttpStatus.NOT_FOUND);
        }
        condition.form = orgForm._id;
      }

      const data = await this.formValueService.find(condition, exportToExcel);

      if (exportToExcel) {
        response.setHeader(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );

        response.setHeader(
          'Content-Disposition',
          'attachment; filename=form_values.xlsx',
        );

        response.end(data);
      }

      return data
        ? response.send({
            status: true,
            message: 'Form values fetched successfully',
            data,
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to create form' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Documents and values based on app, organization
   * @returns Promise<Response>
   */
  @Get('documents/:onboarding_employee_id')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async getDocuments(
    @Req() request: Request,
    @Res() response: Response,
    @Param('onboarding_employee_id') onboarding_employee_id?: string,
  ): Promise<Response> {
    try {
      const documents = await this.formValueService.getDocumentsInfo(
        request.user['organization_id'],
        onboarding_employee_id,
      );

      return documents
        ? response.send({
            status: true,
            message: 'Form values document list fetched successfully',
            data: documents,
          })
        : response
            .status(HttpStatus.CONFLICT)
            .send({ status: false, message: 'Unable to create form' });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

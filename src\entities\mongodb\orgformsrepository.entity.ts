import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import {
  SchemaOptions,
  SchemaTypes,
  ObjectId,
  HydratedDocument,
} from 'mongoose';
import { GROUP, CREATED_BY } from 'src/util/interfaces/forms.interface';
import { FormsRepository } from './formsrepository.entity';
import {
  SECTION_VIEW_TYPE,
  TABLET_LAYOUT,
} from 'src/util/interfaces/theme.interface';
import { FormResponseAsEmailRepository } from './form-response-as-email-repository.entity';

const orgFormsRepoSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'org_forms_repository',
  versionKey: false,
};
@Schema(orgFormsRepoSchemaOptions)
export class OrgFormsRepository {
  @Prop({ type: SchemaTypes.ObjectId })
  id: ObjectId;

  @Prop({ type: SchemaTypes.String, unique: true, required: true })
  form_id: string;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'FormsRepository' })
  original_form_id: FormsRepository;

  @Prop({ type: SchemaTypes.String, required: true })
  name: string;

  @Prop({ type: SchemaTypes.String, default: null })
  icon: string;

  @Prop({ type: SchemaTypes.String, default: null })
  description: string;

  @Prop({ type: SchemaTypes.Mixed })
  fields: Record<string, GROUP>;

  @Prop({ type: SchemaTypes.Mixed })
  validationschema: any;

  @Prop({ type: SchemaTypes.String, required: true })
  organization: string;

  @Prop({ type: SchemaTypes.String, default: SECTION_VIEW_TYPE.plain })
  sectionViewType: SECTION_VIEW_TYPE;

  @Prop({ type: SchemaTypes.String, default: TABLET_LAYOUT.singleColumn })
  tabletLayout: TABLET_LAYOUT;

  @Prop({ type: SchemaTypes.Number, default: 0 })
  order_position: number;

  @Prop({ type: SchemaTypes.String })
  app_id: string;

  @Prop({ type: SchemaTypes.Boolean, default: false })
  has_sub_forms: boolean;

  @Prop({ type: SchemaTypes.Boolean, default: false })
  is_sub_form: boolean;

  @Prop({ type: SchemaTypes.Boolean, default: false })
  is_quiz_form: boolean;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'OrgFormsRepository' })
  main_form_id: ObjectId;

  @Prop({ type: [{ type: SchemaTypes.ObjectId, ref: 'OrgFormsRepository' }] })
  sub_forms: OrgFormsRepository[];

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'FormResponseAsEmailRepository',
    required: false,
  })
  form_response_as_email: FormResponseAsEmailRepository;

  @Prop({ type: SchemaTypes.Mixed })
  created_by: CREATED_BY;

  @Prop({ type: SchemaTypes.Mixed })
  edited_by: CREATED_BY[];

  @Prop({ type: SchemaTypes.Mixed })
  trashed_by: CREATED_BY[];

  @Prop({ type: SchemaTypes.Mixed })
  restored_by: CREATED_BY[];

  @Prop({ default: true, type: SchemaTypes.Boolean })
  is_disable_form_response: boolean;

  @Prop({ default: false, type: SchemaTypes.Boolean })
  has_multiple_form_responses: boolean;

  @Prop({ default: false, type: SchemaTypes.Boolean })
  status: boolean;

  @Prop({ type: SchemaTypes.Date })
  deleted_at: Date;
}

export type OrgFormsRepositoryDocument = HydratedDocument<OrgFormsRepository>;
export const OrgFormsRepositorySchema =
  SchemaFactory.createForClass(OrgFormsRepository);

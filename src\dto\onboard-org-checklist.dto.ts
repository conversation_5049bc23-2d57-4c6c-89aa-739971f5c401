import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsO<PERSON>,
} from 'class-validator';
import { CheckListItem } from 'src/entities/mongodb/onboard-org-checklist-repository.entity';

export class OnBoardOrgChecklistRepoDto {
  // @IsString()
  // @IsNotEmpty()
  // checklist_type: string;

  // @IsString()
  // @IsNotEmpty()
  // checklist_name: string;

  @IsNotEmpty()
  @IsArray()
  @IsObject({ each: true })
  checklist: CheckListItem[];
}

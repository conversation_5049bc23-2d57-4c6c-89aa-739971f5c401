import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { EMPLOYEE_STATUS } from 'src/entities/onboarding-employee.entity';

export class OnboardingEmployeeDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  mobile_number: string;

  @IsNotEmpty()
  password: string;

  @IsOptional()
  @IsString()
  @IsEnum(EMPLOYEE_STATUS)
  status: EMPLOYEE_STATUS;
}

export class UpdateOnboardingEmployeeDto {
  @IsOptional()
  @IsString()
  name: string;

  @IsOptional()
  @IsEmail()
  email: string;

  @IsOptional()
  mobile_number: string;

  @IsOptional()
  password: string;

  @IsOptional()
  @IsString()
  @IsEnum(EMPLOYEE_STATUS)
  status: EMPLOYEE_STATUS;

  @IsArray()
  @IsOptional()
  appIds: [String];

  @IsOptional()
  @IsString()
  scheduled_interview_time: string;

  @IsOptional()
  @IsString()
  scheduled_interview_date: string;

  @IsOptional()
  bg_verification_document: any;
}

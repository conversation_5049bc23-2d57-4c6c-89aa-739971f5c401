name: Prod Build and Deploy
on:
  push:
    branches:
      - production
jobs:
  deploy_job:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: install ssh keys in image vm (- runs-on)
        run: |
          install -m 600 -D /dev/null ~/.ssh/id_rsa
          echo "${{ secrets.PROD_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.PROD_VM_IP }} > ~/.ssh/known_hosts
      - name: run setup file in server
        run: ssh ${{ secrets.PROD_VM_USER }}@${{ secrets.PROD_VM_IP }} "cd ~/github && ./klezaforms.setup.sh && exit"
      - name: cleanup ssh keys in image vm (- runs-on)
        run: rm -rf ~/.ssh

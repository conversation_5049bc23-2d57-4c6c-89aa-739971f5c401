import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { IndustryTypes } from './industry-types.entity';
import { Apps } from './apps.entity';

@Entity()
export class IndustryAppProcess {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  industry_app_process_id: string;

  @Column()
  process_name: string;

  @Column({ unique: true })
  process_code: string;

  @Column()
  description: string;

  @Column({ type: Boolean, default: true })
  status: boolean;

  @OneToMany(() => Apps, (apps) => apps.industry_app_process)
  apps: Apps[];

  @ManyToOne(
    () => IndustryTypes,
    (industry_types: IndustryTypes) => industry_types.id,
    { eager: false, nullable: false },
  )
  @JoinColumn({ name: 'industry_type_id' })
  industry_types: IndustryTypes;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

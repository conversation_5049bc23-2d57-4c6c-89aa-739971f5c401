//Nest Imports
import {
  Controller,
  HttpException,
  HttpStatus,
  Res,
  Post,
  UsePipes,
  ValidationPipe,
  UseGuards,
  Body,
  Req,
  Param,
  Patch,
  Get,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

//Library Imports
import { Response, Request } from 'express';

//Root Imports
import { OrgAppConfigurationDto } from 'src/dto/org-app-configuration.dto';
import {
  CreateOrgAppDbConfigDto,
  UpdateOrgAppDbConfigDto,
} from 'src/dto/org-app-dashboard-configuration.dto';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { OrgAppDashboardConfigurationService } from 'src/services/org-app-dashbaord-configuration.service';

@Controller('org-app-dashboard-configuration')
export default class OrgAppDashboardConfigurationController {
  constructor(
    private readonly OrgAppDbConfigService: OrgAppDashboardConfigurationService,
  ) {}

  /**
   * Create Organization App Dashboard Configuration
   * @param createOrgAppDbConfigDto CreateOrgAppDbConfigDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('/:app_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async create(
    @Body() createOrgAppDbConfigDto: CreateOrgAppDbConfigDto,
    @Param('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      return response.status(HttpStatus.CREATED).send({
        status: true,
        message: 'Dashboard configuration details saved successfully .',
        data: await this.OrgAppDbConfigService.create(
          app_id,
          request.user['organization_id'],
          createOrgAppDbConfigDto,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update Organization App Dashboard Configuration
   * @param updateOrgAppDbConfigDto UpdateOrgAppDbConfigDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Patch('/:configuration_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async update(
    @Body() updateOrgAppDbConfigDto: UpdateOrgAppDbConfigDto,
    @Param('configuration_id') configuration_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      return response.status(HttpStatus.ACCEPTED).send({
        status: true,
        message: 'Dashboard configuration details updated successfully .',
        data: await this.OrgAppDbConfigService.edit(
          configuration_id,
          updateOrgAppDbConfigDto,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Organization App Dashboard Configuration Preview Details By App Id
   * @param app_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('preview/:app_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async previewDetailsByAppId(
    @Param('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      return response.status(HttpStatus.OK).send({
        status: true,
        message:
          'Dashboard configuration preview details fetched successfully .',
        data: await this.OrgAppDbConfigService.previewDetailsByAppId({
          app_id,
          organization_id: request.user['organization_id'],
        }),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Organization App Dashboard Configuration Details by app id
   * @param app_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/:app_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async findOne(
    @Param('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      return response.status(HttpStatus.OK).send({
        status: true,
        message: 'Dashboard configuration details fetched successfully .',
        data: await this.OrgAppDbConfigService.findOne({
          app_id,
          organization_id: request.user['organization_id'],
        }),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

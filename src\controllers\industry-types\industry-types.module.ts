import { Module } from '@nestjs/common';
import { UserModule } from '../user/user.module';
import { AppsModule } from '../apps/apps.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { IndustryTypesService } from 'src/services/industry-types.service';
import IndustryTypesController from './industry-types.controller';

@Module({
  imports: [
    UserModule,
    AppsModule,
    TypeOrmModule.forFeature([IndustryTypes], 'mysql'),
    MongooseModule.forFeature([
      // { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
    ]),
  ],
  exports: [],
  providers: [IndustryTypesService],
  controllers: [IndustryTypesController],
})
export class IndustryTypesModule {}

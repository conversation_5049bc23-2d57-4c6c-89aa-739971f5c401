import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Apps } from './apps.entity';
import { Organization } from './organization.entity';
import { IndustryAppProcess } from './industry-app-process.entity';

@Entity()
export class OrgAppConfiguration {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  org_app_configuration_id: string;

  @Column({ nullable: true })
  app_name: string;

  @Column({ nullable: true })
  app_drive_link: string;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'longtext', nullable: true })
  logo: string;

  @ManyToOne(() => Apps, (apps: Apps) => apps.id, {
    eager: false,
    nullable: false,
  })
  @JoinColumn({ name: 'app_id' })
  app: Apps;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @Column({ type: Boolean, default: true })
  status: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

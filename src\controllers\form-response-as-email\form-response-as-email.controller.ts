import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { OrganizationAuth } from 'src/guards/organization.guard';
import { Response } from 'express';

import { FormResponseAsEmailService } from 'src/services/form-response-as-email.service';
import { FormResponseAsEmailDto } from 'src/dto/form-response-as-email.dto';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';

@Controller('form-response-as-email')
export default class FormResponseAsEmailController {
  constructor(
    private formResponseAsEmailService: FormResponseAsEmailService,
    private orgFormsRepositoryService: OrgFormsRepositoryService,
  ) {}

  /**
   * Create FormResponseAsEmailRepository
   * @param formResponseAsEmailDto FormResponseAsEmailDto
   * @param response Response
   * @returns Promise<Response>
   */
  @Post()
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async create(
    @Body() formResponseAsEmailDto: FormResponseAsEmailDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      return response.status(HttpStatus.CREATED).send({
        status: true,
        message: 'Form response as email configuration created successfully.',
        data: await this.formResponseAsEmailService.create(
          formResponseAsEmailDto,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update FormResponseAsEmailRepository
   * @param form_res_as_email_id string
   * @param formResponseAsEmailDto FormResponseAsEmailDto
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Put('/:form_res_as_email_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async update(
    @Param('form_res_as_email_id') form_res_as_email_id: string,
    @Body() formResponseAsEmailDto: FormResponseAsEmailDto,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      return response.status(HttpStatus.OK).send({
        status: true,
        message: 'Form response as email configuration updated successfully.',
        data: await this.formResponseAsEmailService.update(
          form_res_as_email_id,
          formResponseAsEmailDto,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get FormResponseAsEmailRepository Form Id
   * @param form_id string
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/:form_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async findOne(
    @Param('form_id') form_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const form = await this.orgFormsRepositoryService.findOne(
        {
          form_id,
        },
        false,
        {
          id: true,
        },
      );

      if (!form) {
        throw new BadRequestException(
          'Invalid form id! Please send valid form id',
        );
      }

      return response.status(HttpStatus.OK).send({
        status: true,
        message:
          'Form response as email configuration details fetched successfully .',
        data: await this.formResponseAsEmailService.findOne({
          form,
        }),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

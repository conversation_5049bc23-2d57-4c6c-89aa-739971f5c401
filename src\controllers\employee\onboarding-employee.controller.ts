import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { OrganizationAuth } from 'src/guards/organization.guard';
import { Request, Response } from 'express';

import { FindOptionsWhere, Not } from 'typeorm';
import { AppsService } from 'src/services/apps.service';
import { OrganizationService } from 'src/services/organization.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import {
  OnboardingEmployeeDto,
  UpdateOnboardingEmployeeDto,
} from 'src/dto/onboarding-employee.dto';
import {
  EMPLOYEE_STATUS,
  OnboardingEmployee,
} from 'src/entities/onboarding-employee.entity';
@Controller('onboarding-employee')
export default class OnboardingEmployeeController {
  constructor(
    private onboardingEmployeeService: OnboardingEmployeeService,
    private readonly appService: AppsService,
    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Create OnboardingEmployee
   * @param onboardingEmployeeDto OnboardingEmployeeDto
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Post()
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async create(
    @Body() onboardingEmployeeDto: OnboardingEmployeeDto,
    // @Param('app_id') app_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: OnboardingEmployee =
        await this.onboardingEmployeeService.create(
          onboardingEmployeeDto,
          // app_id,
          request.user['organization_id'],
        );

      return data
        ? response.status(HttpStatus.CREATED).send({
            status: true,
            message: 'OnboardingEmployee created successfully .',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to create the onboarding_employee.',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update OnboardingEmployee
   * @param onboarding_employee_id string
   * @param onboardingEmployeeDto UpdateOnboardingEmployeeDto
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Put('/:onboarding_employee_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async update(
    @Param('onboarding_employee_id') onboarding_employee_id: string,
    @Body() onboardingEmployeeDto: UpdateOnboardingEmployeeDto,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: OnboardingEmployee =
        await this.onboardingEmployeeService.update(
          onboarding_employee_id,
          onboardingEmployeeDto,
        );

      return data
        ? response.status(HttpStatus.OK).send({
            status: true,
            message: 'OnboardingEmployee updated successfully .',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to create the onboarding_employee.',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // /**
  //  * Update OnboardingEmployee Status
  //  * @param onboarding_employee_id string
  //  * @param request Request
  //  * @param response Response
  //  * @returns Promise<Response>
  //  */
  // @Patch('/:onboarding_employee_id')
  // @UsePipes(ValidationPipe)
  // @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  // async onBoardOnboardingEmployee(
  //   @Param('onboarding_employee_id') onboarding_employee_id: string,
  //   @Body() body: any,
  //   @Res() response: Response,
  // ): Promise<Response> {
  //   try {
  //     if (!body?.apps || !body?.apps?.length) {
  //       return response.status(HttpStatus.BAD_REQUEST).send({
  //         status: false,
  //         message: 'Please select at least one app.',
  //       });
  //     }

  //     const data: Boolean =
  //       await this.onboardingEmployeeService.onBoardOnboardingEmployee(
  //         onboarding_employee_id,
  //         body?.apps,
  //       );

  //     return data
  //       ? response.status(HttpStatus.OK).send({
  //           status: true,
  //           message: 'Applicant onboard successfully',
  //         })
  //       : response.status(HttpStatus.CONFLICT).send({
  //           status: false,
  //           message: 'Unable to create the onboarding_employee.',
  //         });
  //   } catch (error) {
  //     throw new HttpException(
  //       error?.message || 'Something went wrong. Please try again later.',
  //       error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  /**
   * Get OnboardingEmployee by Id
   * @param onboarding_employee_id string
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/:onboarding_employee_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async findOne(
    @Param('onboarding_employee_id') onboarding_employee_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const organization_id = request?.user['organization_id'];

      return response.status(HttpStatus.OK).send({
        status: true,
        message: 'OnboardingEmployee fetched successfully .',
        data: await this.onboardingEmployeeService.findOneWithFullDetails(
          {
            onboarding_employee_id,
          },
          false,
          this.onboardingEmployeeService.select,
          null,
          organization_id,
        ),
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get OnboardingEmployees
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async find(
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const findBy: FindOptionsWhere<OnboardingEmployee> = {
        organization,
        status: Not(EMPLOYEE_STATUS.EMPLOYEE_ONBOARD_COMPLETED),
      };

      const data: any = await this.onboardingEmployeeService.find(findBy);

      return data
        ? response.status(HttpStatus.OK).send({
            status: true,
            data,
            message: 'OnboardingEmployees fetched successfully .',
          })
        : response.status(HttpStatus.CONFLICT).send({
            status: false,
            message: 'Unable to create the onboarding_employee.',
          });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

import { FindOptionsSelect, FindOptionsWhere, Repository } from 'typeorm';
import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { generateUUID } from 'src/util';
import { AppsService } from './apps.service';
import { ThemesDto } from 'src/dto/themes.dto';
import { Apps } from 'src/entities/apps.entity';
import { Themes } from 'src/entities/themes.entity';
import { OrganizationService } from './organization.service';
import { Organization } from 'src/entities/organization.entity';
import { MOBILE_LAYOUT } from 'src/util/interfaces/theme.interface';

@Injectable()
export class ThemesService {
  constructor(
    private AppsService: AppsService,
    @InjectRepository(Themes, 'mysql')
    private ThemesRepository: Repository<Themes>,
    private OrganizationService: OrganizationService,
  ) {}

  /**
   *  Select object
   */
  public select: FindOptionsSelect<Themes> = {
    id: false,
    name: true,
    theme: {
      bodyFont: true,
      titleFont: true,
      linkColor: true,
      textColor: true,
      buttonColor: true,
      buttonStyle: true,
      backgroundColor: true,
      buttonTextColor: true,
    },
    theme_id: true,
    status: true,
  };

  /**
   * Get app theme by condition
   * @param findBy FindOptionsWhere<Themes>
   * @param withDeleted boolean
   * @param select FindOptionsSelect<Themes>
   * @returns  Promise<Themes[]>
   */
  async findOne(
    findBy: FindOptionsWhere<Themes>,
    withDeleted: boolean = false,
    select: FindOptionsSelect<Themes> = this.select,
  ): Promise<Themes> {
    try {
      return await this.ThemesRepository.findOne({
        select,
        withDeleted,
        where: findBy,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all app themes
   * @param findBy FindOptionsWhere<Themes>
   * @param withDeleted boolean
   * @param select FindOptionsSelect<Themes>
   * @returns  Promise<Themes[]>
   */
  async list(
    findBy: FindOptionsWhere<Themes> = null,
    withDeleted: boolean = false,
    select: FindOptionsSelect<Themes> = this.select,
  ): Promise<Themes[]> {
    try {
      return await this.ThemesRepository.find({
        select,
        withDeleted,
        where: findBy,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  /**
   * Return Organization
   * @parma organization_id string
   * @return Promise<Organization>
   */
  async getOrganization(organization_id: string): Promise<Organization> {
    const data = await this.OrganizationService.findOne(
      { organization_id },
      false,
      { id: true },
    );
    if (data) return data;
    throw new NotFoundException('Organization not found');
  }

  /**
   * Return App
   * @parma app_id string
   * @return Promise<Apps>
   */
  async getApp(app_id: string): Promise<Apps> {
    const data = await this.AppsService.findOne({ app_id }, false, {
      id: true,
    });
    if (data) return data;
    throw new NotFoundException('App not found');
  }

  /**
   * insert operation
   * @param body ThemesDto
   * @param app_id string
   * @param organiztion_id string
   * @returns Promise<Boolean>
   */
  async insert(
    body: ThemesDto,
    app_id: string,
    organiztion_id: string,
  ): Promise<Boolean> {
    try {
      const organization = await this.getOrganization(organiztion_id);
      const app = await this.getApp(app_id);
      await this.ThemesRepository.query(
        `UPDATE ${Themes.name?.toLowerCase()} set status = false where EXISTS (SELECT * FROM app_themes where app_id = ${
          app.id
        }) and organizationId = ${organization.id}`,
      );
      return (await this.ThemesRepository.save({
        ...body,
        mobileLayout: MOBILE_LAYOUT.singleColumn,
        apps: [app],
        organization,
        theme_id: generateUUID(),
      }))
        ? true
        : false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * update operation
   * @param body ThemesDto
   * @param theme_id string
   * @param organiztion_id string
   * @returns Promise<Boolean>
   */
  async update(
    theme_id: string,
    body: ThemesDto,
    organiztion_id: string,
  ): Promise<Boolean> {
    try {
      const organization = await this.getOrganization(organiztion_id);
      const data: Themes = await this.findOne(
        { theme_id, organization },
        false,
        {
          ...this.select,
          id: true,
        },
      );
      if (!data)
        throw new HttpException('Theme Not Found', HttpStatus.NOT_FOUND);
      return (await this.ThemesRepository.save({
        id: data.id,
        ...body,
        mobileLayout: MOBILE_LAYOUT.singleColumn,
      }))
        ? true
        : false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Activate Theme
   * @param app_id string
   * @param organiztion_id string
   * @returns Promise<Boolean>
   */
  async activateTheme(
    app_id: string,
    organiztion_id: string,
    theme_id: string,
  ): Promise<Boolean> {
    try {
      const organization = await this.getOrganization(organiztion_id);

      const app = await this.getApp(app_id);

      await this.ThemesRepository.query(
        `UPDATE ${Themes.name?.toLowerCase()} set status = false where EXISTS (SELECT * FROM app_themes where app_id = ${
          app.id
        }) and organizationId = ${organization.id}`,
      );

      return (await this.ThemesRepository.query(
        `UPDATE ${Themes.name?.toLowerCase()} set status = true where theme_id = '${theme_id}'`,
      ))
        ? true
        : false;
    } catch (error) {
      throw error;
    }
  }
}

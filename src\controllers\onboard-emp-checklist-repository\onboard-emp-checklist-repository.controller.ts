import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport/dist/auth.guard';
import { OnBoardEmpChecklistRepoDto } from 'src/dto/onboard-emp-checklist.dto';
import { OnBoardEmpChecklistRepository } from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import { Request, Response } from 'express';
import { AppService } from 'src/app.service';
import { OrganizationService } from 'src/services/organization.service';
import { AppsService } from 'src/services/apps.service';
import { QueryOptions } from 'mongoose';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';

@Controller('onboard-emp-checklist')
export default class OnBoardEmpChecklistController {
  constructor(
    private readonly OnBoardEmpChecklistService: OnBoardEmpChecklistService,
    private readonly OnboardingEmployeeService: OnboardingEmployeeService,
  ) {}

  /**
   * Create OnBoardEmpChecklist
   * @param OnBoardEmpChecklistRepoDto OnBoardEmpChecklistRepoDto
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Post('/:onboarding_employee_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async create(
    @Body() OnBoardEmpChecklistRepoDto: OnBoardEmpChecklistRepoDto,
    @Param('onboarding_employee_id') onboarding_employee_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const data: OnBoardEmpChecklistRepository =
        await this.OnBoardEmpChecklistService.create(
          OnBoardEmpChecklistRepoDto,
          onboarding_employee_id,
          request?.user['organization_id'],
        );

      return response
        .status(data ? HttpStatus.CREATED : HttpStatus.CONFLICT)
        .send({
          status: data ? true : false,
          message: data
            ? 'Onboard Employee checklist created successfully .'
            : 'Unable to create the Onboard Employee checklist.',
        });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Onboard onboarding_employee checklist by id
   * @param onboarding_employee_id string
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('/:onboarding_employee_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async findOne(
    @Param('onboarding_employee_id') onboarding_employee_id: string,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const onboarding_employee = await this.OnboardingEmployeeService.findOne(
        { onboarding_employee_id },
        false,
        { id: true },
      );

      if (!onboarding_employee) {
        throw new HttpException(
          'Onboarding Employee not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const data: OnBoardEmpChecklistRepository =
        await this.OnBoardEmpChecklistService.findOne({
          onboarding_employee_id,
        });

      return response.status(HttpStatus.OK).send({
        status: true,
        data,
        message: 'Onboard Employee checklist fetched successfully .',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // /**
  //  * Get OnBoardEmpChecklists
  //  * @param request Request
  //  * @param response Response
  //  * @returns Promise<Response>
  //  */
  // @Get('')
  // @UsePipes(ValidationPipe)
  // @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  // async find(
  //   @Query('app_id') app_id: string,
  //   @Req() request: Request,
  //   @Res() response: Response,
  // ): Promise<Response> {
  //   try {
  //     const app = await this.appService.findOne({ app_id }, false, {
  //       id: true,
  //     });

  //     if (!app) {
  //       throw new HttpException('App not found', HttpStatus.NOT_FOUND);
  //     }

  //     const organization = await this.organizationService.findOne(
  //       {
  //         organization_id: request?.user['organization_id'],
  //       },
  //       false,
  //       { id: true },
  //     );

  //     if (!organization) {
  //       throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
  //     }

  //     const findBy: QueryOptions<OnBoardEmpChecklistRepository> = {
  //       app_id,
  //       organization_id: request?.user['organization_id'],
  //     };

  //     const data: OnBoardEmpChecklistRepository[] =
  //       await this.OnBoardEmpChecklistService.findAll(findBy);

  //     return data
  //       ? response.status(HttpStatus.OK).send({
  //           status: true,
  //           data,
  //           message:
  //             'Onboard onboarding_employee checklist fetched successfully .',
  //         })
  //       : response.status(HttpStatus.CONFLICT).send({
  //           status: false,
  //           message: 'Unable to create the onboarding_employee.',
  //         });
  //   } catch (error) {
  //     throw new HttpException(
  //       error?.message || 'Something went wrong. Please try again later.',
  //       error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }
}
